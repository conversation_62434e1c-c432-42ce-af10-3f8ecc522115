"""
Base Azure service class with common functionality
"""
import logging
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from azure.mgmt.compute import ComputeManagementClient
from azure.mgmt.network import NetworkManagementClient
from azure.mgmt.resource import ResourceManagementClient
from azure.core.exceptions import AzureError, ClientAuthenticationError
from django.conf import settings


logger = logging.getLogger(__name__)


class AzureServiceBase:
    """Base class for Azure services with common functionality"""
    
    def __init__(self, subscription_id, tenant_id=None, client_id=None, client_secret=None):
        self.subscription_id = subscription_id
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.credential = None
        self._clients = {}
        
    def get_credential(self):
        """Get or create Azure credential"""
        if not self.credential:
            try:
                if self.client_id and self.client_secret and self.tenant_id:
                    # Use service principal authentication
                    self.credential = ClientSecretCredential(
                        tenant_id=self.tenant_id,
                        client_id=self.client_id,
                        client_secret=self.client_secret
                    )
                    logger.info(f"Azure service principal credential created for subscription {self.subscription_id}")
                else:
                    # Use default credential (managed identity, Azure CLI, etc.)
                    self.credential = DefaultAzureCredential()
                    logger.info(f"Azure default credential created for subscription {self.subscription_id}")
                    
            except (AzureError, ClientAuthenticationError) as e:
                logger.error(f"Failed to create Azure credential: {e}")
                raise
        return self.credential
    
    def get_compute_client(self):
        """Get or create Azure Compute Management client"""
        if 'compute' not in self._clients:
            credential = self.get_credential()
            self._clients['compute'] = ComputeManagementClient(
                credential=credential,
                subscription_id=self.subscription_id
            )
        return self._clients['compute']
    
    def get_network_client(self):
        """Get or create Azure Network Management client"""
        if 'network' not in self._clients:
            credential = self.get_credential()
            self._clients['network'] = NetworkManagementClient(
                credential=credential,
                subscription_id=self.subscription_id
            )
        return self._clients['network']
    
    def get_resource_client(self):
        """Get or create Azure Resource Management client"""
        if 'resource' not in self._clients:
            credential = self.get_credential()
            self._clients['resource'] = ResourceManagementClient(
                credential=credential,
                subscription_id=self.subscription_id
            )
        return self._clients['resource']
    
    def handle_azure_error(self, error, operation):
        """Handle Azure API errors consistently"""
        error_code = getattr(error, 'error_code', 'Unknown')
        error_message = str(error)
        
        logger.error(f"Azure {operation} failed: {error_code} - {error_message}")
        
        # Return structured error info
        return {
            'error': True,
            'error_code': error_code,
            'error_message': error_message,
            'operation': operation
        }
    
    def paginate_results(self, operation, **kwargs):
        """Helper to paginate Azure API results"""
        try:
            results = []
            
            # Azure SDK typically returns iterators that handle pagination automatically
            for item in operation(**kwargs):
                results.append(item)
            
            return results
            
        except AzureError as e:
            logger.error(f"Pagination failed for operation: {e}")
            return []
    
    def get_tags_dict(self, tags):
        """Convert Azure tags to dictionary"""
        if not tags:
            return {}
        
        # Azure tags are already in dictionary format
        return tags if isinstance(tags, dict) else {}
    
    def safe_get(self, data, *keys, default=None):
        """Safely get nested dictionary/object values"""
        for key in keys:
            if hasattr(data, key):
                data = getattr(data, key)
            elif isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return default
        return data
    
    def format_datetime(self, dt):
        """Format datetime for database storage"""
        if dt:
            return dt.replace(tzinfo=None) if dt.tzinfo else dt
        return None
    
    def get_vm_size_specs(self, vm_size, location):
        """Get VM size specifications"""
        try:
            compute_client = self.get_compute_client()
            
            # Get available VM sizes for the location
            vm_sizes = compute_client.virtual_machine_sizes.list(location)
            
            for size in vm_sizes:
                if size.name == vm_size:
                    return {
                        'vcpu_count': size.number_of_cores,
                        'memory_gb': round(size.memory_in_mb / 1024, 2) if size.memory_in_mb else None
                    }

            return {'vcpu_count': None, 'memory_gb': None}

        except Exception as e:
            logger.error(f"Error getting VM size specs for {vm_size}: {str(e)}")
            return {'vcpu_count': None, 'memory_gb': None}
