"""
Management command to load Azure sample data from CSV file
"""
import csv
import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from inventory.models import AzureSubscription, AzureVM, VMTag, VMAgentStatus


class Command(BaseCommand):
    help = 'Load Azure sample data from azure_sample.csv'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='azure_sample.csv',
            help='Path to the CSV file (default: azure_sample.csv)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing Azure data before loading'
        )

    def handle(self, *args, **options):
        csv_file = options['file']
        
        # Check if file exists
        if not os.path.exists(csv_file):
            raise CommandError(f'CSV file "{csv_file}" does not exist.')

        # Clear existing data if requested
        if options['clear']:
            self.stdout.write('Clearing existing Azure data...')
            AzureVM.objects.all().delete()
            AzureSubscription.objects.all().delete()
            VMTag.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing Azure data cleared.'))

        # Load data
        self.stdout.write(f'Loading Azure data from {csv_file}...')
        
        try:
            with transaction.atomic():
                self.load_csv_data(csv_file)
        except Exception as e:
            raise CommandError(f'Error loading data: {str(e)}')

        self.stdout.write(self.style.SUCCESS('Azure sample data loaded successfully!'))

    def load_csv_data(self, csv_file):
        """Load data from CSV file"""
        subscriptions_created = 0
        vms_created = 0
        tags_created = 0
        
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 because of header
                try:
                    # Extract data from CSV row
                    subscription_name = row.get('SubscriptionName', '').strip()
                    business_unit = row.get('BU', '').strip()
                    vm_name = row.get('VMName', '').strip()
                    instance_type = row.get('InstanceType', '').strip()
                    os_version = row.get('OSVersion', '').strip()
                    status = row.get('Status', '').strip()
                    private_ip = row.get('PrivateIpAddress', '').strip()
                    env = row.get('Env', '').strip()
                    uai = row.get('UAI', '').strip()
                    patch_exempt = row.get('PatchExempt', '').strip()
                    patch_provider = row.get('Patch_Provider', '').strip()
                    cto_cloud_ops_managed = row.get('CTOCloudOpsManaged', '').strip()
                    patch = row.get('Patch', '').strip()
                    patch_group = row.get('Patch_Group', '').strip()
                    app_env_cfg_id = row.get('AppEnvCfgID', '').strip()
                    patching_week = row.get('PatchingWeek', '').strip()
                    maintenance_schedule = row.get('MaintenanceSchedule', '').strip()
                    auto_shutdown_schedule = row.get('AutoShutdownSchedule', '').strip()
                    backup = row.get('Backup', '').strip()

                    # Skip rows with missing essential data
                    if not subscription_name or not vm_name:
                        self.stdout.write(
                            self.style.WARNING(f'Row {row_num}: Skipping due to missing subscription name or VM name')
                        )
                        continue

                    # Create or get Azure subscription
                    subscription, created = AzureSubscription.objects.get_or_create(
                        subscription_name=subscription_name,
                        defaults={
                            'subscription_id': f'sub-{subscription_name.lower().replace(" ", "-")}',
                            'business_unit': business_unit,
                            'location': 'East US',  # Default location
                            'is_active': True
                        }
                    )
                    if created:
                        subscriptions_created += 1

                    # Generate VM ID
                    vm_id = f'/subscriptions/{subscription.subscription_id}/resourceGroups/rg-{vm_name.lower()}/providers/Microsoft.Compute/virtualMachines/{vm_name}'

                    # Determine OS type
                    os_type = 'Unknown'
                    if os_version:
                        if 'Windows' in os_version:
                            os_type = 'Windows'
                        elif any(os in os_version.lower() for os in ['linux', 'redhat', 'ubuntu', 'oracle', 'qualys']):
                            os_type = 'Linux'

                    # Create or update Azure VM
                    vm, created = AzureVM.objects.update_or_create(
                        subscription=subscription,
                        name=vm_name,
                        defaults={
                            'vm_id': vm_id,
                            'state': status if status else 'VM unknown',
                            'location': 'East US',  # Default location
                            'instance_type': instance_type,
                            'private_ip_address': private_ip,
                            'os_type': os_type,
                            'os_version': os_version,
                            'resource_group': f'rg-{vm_name.lower()}',
                            
                            # Tag fields from CSV
                            'env_tag': env,
                            'uai_tag': uai,
                            'patch_exempt': patch_exempt,
                            'patch_provider': patch_provider,
                            'cto_cloud_ops_managed': cto_cloud_ops_managed,
                            'patch_tag': patch,
                            'patch_group': patch_group,
                            'app_env_cfg_id': app_env_cfg_id,
                            'patching_week': patching_week,
                            'maintenance_schedule': maintenance_schedule,
                            'auto_shutdown_schedule': auto_shutdown_schedule,
                            'backup_tag': backup,
                            
                            # Raw tags for backup
                            'all_tags_raw': f'Env={env}, UAI={uai}, PatchExempt={patch_exempt}, Patch_Provider={patch_provider}, CTOCloudOpsManaged={cto_cloud_ops_managed}'
                        }
                    )
                    if created:
                        vms_created += 1

                    # Create flattened tags
                    tag_data = [
                        ('Env', env),
                        ('UAI', uai),
                        ('PatchExempt', patch_exempt),
                        ('Patch_Provider', patch_provider),
                        ('CTOCloudOpsManaged', cto_cloud_ops_managed),
                        ('Patch', patch),
                        ('Patch_Group', patch_group),
                        ('AppEnvCfgID', app_env_cfg_id),
                        ('PatchingWeek', patching_week),
                        ('MaintenanceSchedule', maintenance_schedule),
                        ('AutoShutdownSchedule', auto_shutdown_schedule),
                        ('Backup', backup),
                    ]

                    # Clear existing tags for this VM
                    vm.tags.clear()

                    # Add tags with values
                    for key, value in tag_data:
                        if value:  # Only add tags with values
                            tag, tag_created = VMTag.objects.get_or_create(
                                key=key,
                                value=value
                            )
                            vm.tags.add(tag)
                            if tag_created:
                                tags_created += 1

                    # Create VM Agent Status (sample data)
                    VMAgentStatus.objects.update_or_create(
                        vm=vm,
                        defaults={
                            'agent_status': 'Ready' if status == 'VM running' else 'Unknown',
                            'agent_version': '2.7.41491.1010' if os_type == 'Windows' else '2.2.54.2',
                            'extensions_count': 2 if status == 'VM running' else 0,
                            'extensions_status': '[]',
                            'boot_diagnostics_enabled': True if status == 'VM running' else False
                        }
                    )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Row {row_num}: Error processing row - {str(e)}')
                    )
                    continue

        # Print summary
        self.stdout.write(f'Summary:')
        self.stdout.write(f'  - Subscriptions created: {subscriptions_created}')
        self.stdout.write(f'  - VMs created: {vms_created}')
        self.stdout.write(f'  - Tags created: {tags_created}')
        self.stdout.write(f'  - Total VMs in database: {AzureVM.objects.count()}')
        self.stdout.write(f'  - Total subscriptions in database: {AzureSubscription.objects.count()}')
