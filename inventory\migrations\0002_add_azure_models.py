# Generated manually for Azure models

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AzureSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subscription_id', models.CharField(max_length=36, unique=True)),
                ('subscription_name', models.CharField(blank=True, max_length=255)),
                ('tenant_id', models.CharField(blank=True, max_length=36)),
                ('business_unit', models.CharField(max_length=100)),
                ('location', models.Char<PERSON>ield(max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Azure Subscription',
                'verbose_name_plural': 'Azure Subscriptions',
                'ordering': ['business_unit', 'subscription_name'],
            },
        ),
        migrations.CreateModel(
            name='VMTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, max_length=255)),
                ('value', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Azure VM Tag',
                'verbose_name_plural': 'Azure VM Tags',
                'ordering': ['key', 'value'],
                'indexes': [
                    models.Index(fields=['key'], name='inventory_v_key_vm_idx'),
                    models.Index(fields=['key', 'value'], name='inventory_v_key_value_vm_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='AzureVM',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vm_id', models.CharField(db_index=True, max_length=255)),
                ('name', models.CharField(blank=True, db_index=True, max_length=255)),
                ('state', models.CharField(choices=[('VM running', 'Running'), ('VM stopped', 'Stopped'), ('VM deallocated', 'Deallocated'), ('VM starting', 'Starting'), ('VM stopping', 'Stopping'), ('VM deallocating', 'Deallocating'), ('VM unknown', 'Unknown')], db_index=True, max_length=20)),
                ('location', models.CharField(blank=True, db_index=True, max_length=50)),
                ('instance_type', models.CharField(blank=True, db_index=True, max_length=50)),
                ('vcpu_count', models.IntegerField(blank=True, null=True)),
                ('memory_gb', models.FloatField(blank=True, null=True)),
                ('private_ip_address', models.CharField(blank=True, db_index=True, max_length=100)),
                ('public_ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('fqdn', models.CharField(blank=True, max_length=255)),
                ('virtual_network', models.CharField(blank=True, max_length=255)),
                ('subnet', models.CharField(blank=True, max_length=255)),
                ('network_security_group', models.CharField(blank=True, max_length=255)),
                ('os_disk_name', models.CharField(blank=True, max_length=255)),
                ('os_disk_size_gb', models.IntegerField(blank=True, null=True)),
                ('os_disk_type', models.CharField(blank=True, max_length=50)),
                ('data_disk_count', models.IntegerField(default=0)),
                ('os_type', models.CharField(blank=True, max_length=20)),
                ('os_name', models.CharField(blank=True, max_length=255)),
                ('os_version', models.CharField(blank=True, max_length=255)),
                ('resource_group', models.CharField(blank=True, db_index=True, max_length=255)),
                ('availability_set', models.CharField(blank=True, max_length=255)),
                ('availability_zone', models.CharField(blank=True, max_length=10)),
                ('env_tag', models.CharField(blank=True, db_index=True, max_length=100)),
                ('uai_tag', models.CharField(blank=True, db_index=True, max_length=100)),
                ('patch_exempt', models.CharField(blank=True, max_length=100)),
                ('patch_provider', models.CharField(blank=True, max_length=100)),
                ('cto_cloud_ops_managed', models.CharField(blank=True, db_index=True, max_length=100)),
                ('patch_tag', models.CharField(blank=True, max_length=100)),
                ('patch_group', models.CharField(blank=True, max_length=100)),
                ('app_env_cfg_id', models.CharField(blank=True, max_length=100)),
                ('patching_week', models.CharField(blank=True, max_length=100)),
                ('maintenance_schedule', models.CharField(blank=True, max_length=100)),
                ('auto_shutdown_schedule', models.CharField(blank=True, max_length=100)),
                ('backup_tag', models.CharField(blank=True, max_length=100)),
                ('all_tags_raw', models.TextField(blank=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='azure_vms', to='inventory.azuresubscription')),
                ('tags', models.ManyToManyField(blank=True, related_name='azure_vms', to='inventory.vmtag')),
            ],
            options={
                'verbose_name': 'Azure Virtual Machine',
                'verbose_name_plural': 'Azure Virtual Machines',
                'ordering': ['-last_updated'],
                'indexes': [
                    models.Index(fields=['subscription', 'vm_id'], name='inventory_a_subscr_vm_idx'),
                    models.Index(fields=['state', 'instance_type'], name='inventory_a_state_vm_idx'),
                    models.Index(fields=['env_tag', 'uai_tag'], name='inventory_a_env_uai_idx'),
                    models.Index(fields=['resource_group'], name='inventory_a_resource_group_idx'),
                    models.Index(fields=['cto_cloud_ops_managed'], name='inventory_a_cto_managed_idx'),
                ],
            },
        ),
        migrations.CreateModel(
            name='VMAgentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agent_status', models.CharField(choices=[('Ready', 'Ready'), ('NotReady', 'Not Ready'), ('Unknown', 'Unknown')], default='Unknown', max_length=20)),
                ('agent_version', models.CharField(blank=True, max_length=50)),
                ('last_status_change', models.DateTimeField(blank=True, null=True)),
                ('extensions_count', models.IntegerField(default=0)),
                ('extensions_status', models.TextField(blank=True)),
                ('boot_diagnostics_enabled', models.BooleanField(default=False)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('vm', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='agent_status', to='inventory.azurevm')),
            ],
            options={
                'verbose_name': 'Azure VM Agent Status',
                'verbose_name_plural': 'Azure VM Agent Statuses',
            },
        ),
        migrations.CreateModel(
            name='AzureInventoryRefreshLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('vms_processed', models.IntegerField(default=0)),
                ('errors_count', models.IntegerField(default=0)),
                ('error_details', models.TextField(blank=True)),
                ('triggered_by', models.CharField(blank=True, max_length=100)),
                ('refresh_type', models.CharField(default='manual', max_length=50)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refresh_logs', to='inventory.azuresubscription')),
            ],
            options={
                'verbose_name': 'Azure Inventory Refresh Log',
                'verbose_name_plural': 'Azure Inventory Refresh Logs',
                'ordering': ['-started_at'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='azurevm',
            unique_together={('subscription', 'vm_id')},
        ),
    ]
