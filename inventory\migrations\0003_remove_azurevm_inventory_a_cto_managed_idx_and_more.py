# Generated by Django 4.2.7 on 2025-06-12 16:01

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_add_azure_models'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='azurevm',
            name='inventory_a_cto_managed_idx',
        ),
        migrations.RenameIndex(
            model_name='azurevm',
            new_name='inventory_a_subscri_f6e4dd_idx',
            old_name='inventory_a_subscr_vm_idx',
        ),
        migrations.RenameIndex(
            model_name='azurevm',
            new_name='inventory_a_state_332fcb_idx',
            old_name='inventory_a_state_vm_idx',
        ),
        migrations.RenameIndex(
            model_name='azurevm',
            new_name='inventory_a_env_tag_ffbe40_idx',
            old_name='inventory_a_env_uai_idx',
        ),
        migrations.RenameIndex(
            model_name='azurevm',
            new_name='inventory_a_resourc_9e83b3_idx',
            old_name='inventory_a_resource_group_idx',
        ),
        migrations.RenameIndex(
            model_name='vmtag',
            new_name='inventory_v_key_599f41_idx',
            old_name='inventory_v_key_vm_idx',
        ),
        migrations.RenameIndex(
            model_name='vmtag',
            new_name='inventory_v_key_0f0d39_idx',
            old_name='inventory_v_key_value_vm_idx',
        ),
    ]
