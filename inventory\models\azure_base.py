"""
Azure base models for multi-cloud inventory system
Contains core Azure models like AzureSubscription and shared utilities
"""
from django.db import models
from django.utils import timezone


class AzureSubscription(models.Model):
    """Model to store Azure subscription information"""
    subscription_id = models.CharField(max_length=36, unique=True)  # Azure subscription ID (GUID)
    subscription_name = models.CharField(max_length=255, blank=True)
    tenant_id = models.CharField(max_length=36, blank=True)  # Azure tenant ID
    business_unit = models.CharField(max_length=100)  # BU
    location = models.CharField(max_length=50)  # Azure region/location
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['business_unit', 'subscription_name']
        verbose_name = 'Azure Subscription'
        verbose_name_plural = 'Azure Subscriptions'

    def __str__(self):
        return f"{self.subscription_name} ({self.subscription_id}) - {self.business_unit}"


class AzureInventoryRefreshLog(models.Model):
    """Model to track Azure inventory refresh operations"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    subscription = models.ForeignKey(AzureSubscription, on_delete=models.CASCADE, related_name='refresh_logs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Statistics
    vms_processed = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)
    
    # Metadata
    triggered_by = models.CharField(max_length=100, blank=True)  # User or system
    refresh_type = models.CharField(max_length=50, default='manual')  # manual, scheduled, api

    class Meta:
        ordering = ['-started_at']
        verbose_name = 'Azure Inventory Refresh Log'
        verbose_name_plural = 'Azure Inventory Refresh Logs'

    def __str__(self):
        return f"{self.subscription.subscription_name} - {self.status} ({self.started_at})"

    @property
    def duration(self):
        """Calculate refresh duration"""
        if self.completed_at and self.started_at:
            return self.completed_at - self.started_at
        return None
