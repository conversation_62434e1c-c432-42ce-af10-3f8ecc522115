"""
Azure VM-related models for multi-cloud inventory system
Contains AzureVM, VMTag, and VMAgentStatus models
"""
from django.db import models
from django.utils import timezone
from .azure_base import AzureSubscription


class VMTag(models.Model):
    """Model to store flattened Azure VM tags as key-value pairs"""
    key = models.CharField(max_length=255, db_index=True)
    value = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['key', 'value']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['key', 'value']),
        ]
        verbose_name = 'Azure VM Tag'
        verbose_name_plural = 'Azure VM Tags'

    def __str__(self):
        return f"{self.key}={self.value}"


class AzureVM(models.Model):
    """Model to store Azure Virtual Machine information"""

    # VM State choices (Azure VM power states)
    STATE_CHOICES = [
        ('VM running', 'Running'),
        ('VM stopped', 'Stopped'),
        ('VM deallocated', 'Deallocated'),
        ('VM starting', 'Starting'),
        ('VM stopping', 'Stopping'),
        ('VM deallocating', 'Deallocating'),
        ('VM unknown', 'Unknown'),
    ]

    # Core identification
    subscription = models.ForeignKey(AzureSubscription, on_delete=models.CASCADE, related_name='azure_vms')
    vm_id = models.CharField(max_length=255, db_index=True)  # Generated from subscription + VM name
    name = models.CharField(max_length=255, blank=True, db_index=True)  # VMName from CSV
    state = models.CharField(max_length=20, choices=STATE_CHOICES, db_index=True)  # Status from CSV
    location = models.CharField(max_length=50, db_index=True, blank=True)  # Azure region

    # VM specifications (from CSV InstanceType field)
    instance_type = models.CharField(max_length=50, blank=True, db_index=True)  # InstanceType from CSV
    vcpu_count = models.IntegerField(null=True, blank=True)
    memory_gb = models.FloatField(null=True, blank=True)

    # Network information
    private_ip_address = models.CharField(max_length=100, blank=True, db_index=True)  # PrivateIpAddress from CSV
    public_ip_address = models.GenericIPAddressField(null=True, blank=True)
    fqdn = models.CharField(max_length=255, blank=True)
    virtual_network = models.CharField(max_length=255, blank=True)
    subnet = models.CharField(max_length=255, blank=True)

    # Security and access
    network_security_group = models.CharField(max_length=255, blank=True)

    # Storage information
    os_disk_name = models.CharField(max_length=255, blank=True)
    os_disk_size_gb = models.IntegerField(null=True, blank=True)
    os_disk_type = models.CharField(max_length=50, blank=True)
    data_disk_count = models.IntegerField(default=0)

    # OS information (from CSV OSVersion field)
    os_type = models.CharField(max_length=20, blank=True)  # Derived from OSVersion (Windows/Linux)
    os_name = models.CharField(max_length=255, blank=True)
    os_version = models.CharField(max_length=255, blank=True)  # OSVersion from CSV

    # Azure-specific information
    resource_group = models.CharField(max_length=255, blank=True, db_index=True)
    availability_set = models.CharField(max_length=255, blank=True)
    availability_zone = models.CharField(max_length=10, blank=True)

    # Tag fields matching CSV structure
    env_tag = models.CharField(max_length=100, blank=True, db_index=True)  # Env from CSV
    uai_tag = models.CharField(max_length=100, blank=True, db_index=True)  # UAI from CSV
    patch_exempt = models.CharField(max_length=100, blank=True)  # PatchExempt from CSV
    patch_provider = models.CharField(max_length=100, blank=True)  # Patch_Provider from CSV
    cto_cloud_ops_managed = models.CharField(max_length=100, blank=True, db_index=True)  # CTOCloudOpsManaged from CSV
    patch_tag = models.CharField(max_length=100, blank=True)  # Patch from CSV
    patch_group = models.CharField(max_length=100, blank=True)  # Patch_Group from CSV
    app_env_cfg_id = models.CharField(max_length=100, blank=True)  # AppEnvCfgID from CSV
    patching_week = models.CharField(max_length=100, blank=True)  # PatchingWeek from CSV
    maintenance_schedule = models.CharField(max_length=100, blank=True)  # MaintenanceSchedule from CSV
    auto_shutdown_schedule = models.CharField(max_length=100, blank=True)  # AutoShutdownSchedule from CSV
    backup_tag = models.CharField(max_length=100, blank=True)  # Backup from CSV

    # Flattened tags relationship
    tags = models.ManyToManyField(VMTag, blank=True, related_name='azure_vms')
    all_tags_raw = models.TextField(blank=True)  # Raw tags string for backup

    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['subscription', 'vm_id']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['subscription', 'vm_id']),
            models.Index(fields=['state', 'instance_type']),
            models.Index(fields=['env_tag', 'uai_tag']),
            models.Index(fields=['resource_group']),
        ]
        verbose_name = 'Azure Virtual Machine'
        verbose_name_plural = 'Azure Virtual Machines'

    def __str__(self):
        return f"{self.name or self.vm_id} ({self.instance_type}) - {self.subscription.subscription_name}"

    def get_business_unit(self):
        """Get business unit from subscription"""
        return self.subscription.business_unit

    def get_subscription_name(self):
        """Get subscription name"""
        return self.subscription.subscription_name

    def get_subscription_id(self):
        """Get subscription ID"""
        return self.subscription.subscription_id

    def get_location(self):
        """Get location"""
        return self.location

    def get_os_type(self):
        """Derive OS type from OS version"""
        if self.os_version:
            if 'Windows' in self.os_version:
                return 'Windows'
            elif any(os in self.os_version.lower() for os in ['linux', 'redhat', 'ubuntu', 'oracle', 'qualys']):
                return 'Linux'
        return 'Unknown'


class VMAgentStatus(models.Model):
    """Model to store Azure VM Agent status information"""
    
    AGENT_STATUS_CHOICES = [
        ('Ready', 'Ready'),
        ('NotReady', 'Not Ready'),
        ('Unknown', 'Unknown'),
    ]

    vm = models.OneToOneField(AzureVM, on_delete=models.CASCADE, related_name='agent_status')
    
    # Agent status information
    agent_status = models.CharField(max_length=20, choices=AGENT_STATUS_CHOICES, default='Unknown')
    agent_version = models.CharField(max_length=50, blank=True)
    last_status_change = models.DateTimeField(null=True, blank=True)
    
    # Extensions information
    extensions_count = models.IntegerField(default=0)
    extensions_status = models.TextField(blank=True)  # JSON array of extension statuses
    
    # Boot diagnostics
    boot_diagnostics_enabled = models.BooleanField(default=False)
    
    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Azure VM Agent Status'
        verbose_name_plural = 'Azure VM Agent Statuses'

    def __str__(self):
        return f"{self.vm.name} - Agent: {self.agent_status}"
