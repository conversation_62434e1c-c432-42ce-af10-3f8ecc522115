from rest_framework import serializers
from .models import (
    AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag,
    EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag,
    AzureSubscription, AzureVM, VMTag, VMAgentStatus, AzureInventoryRefreshLog
)
import json


class InstanceTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = InstanceTag
        fields = ['key', 'value']


class AWSAccountSerializer(serializers.ModelSerializer):
    ec2_instances_count = serializers.SerializerMethodField()

    class Meta:
        model = AWSAccount
        fields = '__all__'

    def get_ec2_instances_count(self, obj):
        return obj.ec2_instances.count()


class SSMStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = SSMStatus
        fields = '__all__'


class EC2InstanceMainInventorySerializer(serializers.ModelSerializer):
    """Main inventory table serializer with core fields"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_status = serializers.CharField(source='ssm_status.ping_status', read_only=True)

    class Meta:
        model = EC2Instance
        fields = [
            'business_unit', 'account_name', 'account_id', 'region',
            'instance_id', 'state', 'name', 'os_information',
            'private_ip_address', 'instance_type', 'vcpu_count',
            'memory_gb', 'ssm_status'
        ]


class EC2InstanceDetailedSerializer(serializers.ModelSerializer):
    """Detailed instance view serializer with all fields"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_status = SSMStatusSerializer(read_only=True)
    instance_tags = InstanceTagSerializer(source='tags', many=True, read_only=True)
    security_groups_list = serializers.SerializerMethodField()

    class Meta:
        model = EC2Instance
        fields = [
            # Core identification
            'business_unit', 'account_name', 'account_id', 'region',
            'instance_id', 'state', 'name', 'os_information',

            # Network and access
            'private_ip_address', 'instance_type', 'vcpu_count', 'memory_gb',

            # Storage
            'root_volume_id', 'root_volume_size_gb', 'data_disk_count',

            # Tags
            'env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id',
            'maintenance_schedule', 'schedule_tag', 'backup_tag',
            'cto_cloud_ops_managed', 'patch_provider', 'patch_group', 'patch_exempt',

            # Infrastructure details
            'ami_id', 'instance_profile', 'security_groups_list', 'subnet_id', 'subnet_name',

            # SSM and tags
            'ssm_status', 'instance_tags', 'all_tags_raw',

            # Metadata
            'last_updated', 'created_at'
        ]

    def get_security_groups_list(self, obj):
        """Parse security groups from JSON string"""
        try:
            if obj.security_groups:
                return json.loads(obj.security_groups)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


class EC2InstanceListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_ping_status = serializers.CharField(source='ssm_status.ping_status', read_only=True)

    class Meta:
        model = EC2Instance
        fields = [
            'id', 'instance_id', 'name', 'instance_type', 'state',
            'private_ip_address', 'os_information', 'env_tag', 'uai_tag',
            'cto_cloud_ops_managed', 'business_unit', 'account_name', 'account_id',
            'region', 'ssm_ping_status', 'last_updated'
        ]


# Backward compatibility
EC2InstanceSerializer = EC2InstanceDetailedSerializer


class InventoryRefreshLogSerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = InventoryRefreshLog
        fields = '__all__'
    
    def get_duration(self, obj):
        if obj.completed_at and obj.started_at:
            delta = obj.completed_at - obj.started_at
            return str(delta)
        return None


class InventoryStatsSerializer(serializers.Serializer):
    """Serializer for dashboard statistics"""
    total_accounts = serializers.IntegerField()
    total_instances = serializers.IntegerField()
    running_instances = serializers.IntegerField()
    stopped_instances = serializers.IntegerField()
    ssm_online = serializers.IntegerField()
    ssm_offline = serializers.IntegerField()
    last_refresh = serializers.DateTimeField()


class EKSClusterTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = EKSClusterTag
        fields = ['key', 'value']


class EKSNodeGroupSerializer(serializers.ModelSerializer):
    """Serializer for EKS node groups"""
    instance_types_list = serializers.SerializerMethodField()
    subnet_ids_list = serializers.SerializerMethodField()

    class Meta:
        model = EKSNodeGroup
        fields = [
            'node_group_name', 'arn', 'status', 'capacity_type',
            'instance_types_list', 'ami_type', 'desired_size', 'min_size', 'max_size',
            'subnet_ids_list', 'remote_access_enabled', 'disk_size',
            'created_at_aws', 'modified_at_aws', 'last_updated'
        ]

    def get_instance_types_list(self, obj):
        """Parse instance types from JSON string"""
        try:
            if obj.instance_types:
                return json.loads(obj.instance_types)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_subnet_ids_list(self, obj):
        """Parse subnet IDs from JSON string"""
        try:
            if obj.subnet_ids:
                return json.loads(obj.subnet_ids)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


class EKSFargateProfileSerializer(serializers.ModelSerializer):
    """Serializer for EKS Fargate profiles"""
    subnet_ids_list = serializers.SerializerMethodField()
    selectors_list = serializers.SerializerMethodField()

    class Meta:
        model = EKSFargateProfile
        fields = [
            'profile_name', 'arn', 'status', 'pod_execution_role_arn',
            'subnet_ids_list', 'selectors_list', 'created_at_aws', 'last_updated'
        ]

    def get_subnet_ids_list(self, obj):
        """Parse subnet IDs from JSON string"""
        try:
            if obj.subnet_ids:
                return json.loads(obj.subnet_ids)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_selectors_list(self, obj):
        """Parse selectors from JSON string"""
        try:
            if obj.selectors:
                return json.loads(obj.selectors)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


class EKSClusterListSerializer(serializers.ModelSerializer):
    """Simplified serializer for EKS cluster list views"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    addons_list = serializers.SerializerMethodField()

    class Meta:
        model = EKSCluster
        fields = [
            'id', 'cluster_name', 'kubernetes_version', 'platform_version', 'status',
            'cluster_type', 'endpoint', 'node_groups_count', 'fargate_profiles_count',
            'addons_list', 'business_unit', 'account_name', 'account_id', 'region',
            'created_at_aws', 'last_updated'
        ]

    def get_addons_list(self, obj):
        """Parse addons from JSON string"""
        try:
            if obj.addons:
                return json.loads(obj.addons)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


class EKSClusterDetailedSerializer(serializers.ModelSerializer):
    """Detailed EKS cluster serializer with all fields and relationships"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)

    # Parse JSON fields
    subnet_ids_list = serializers.SerializerMethodField()
    security_group_ids_list = serializers.SerializerMethodField()
    public_access_cidrs_list = serializers.SerializerMethodField()
    logging_types_list = serializers.SerializerMethodField()
    addons_list = serializers.SerializerMethodField()
    fargate_profile_names_list = serializers.SerializerMethodField()

    # Related objects
    cluster_tags = EKSClusterTagSerializer(source='tags', many=True, read_only=True)
    node_groups = EKSNodeGroupSerializer(many=True, read_only=True)
    fargate_profiles = EKSFargateProfileSerializer(many=True, read_only=True)

    class Meta:
        model = EKSCluster
        fields = [
            # Core identification
            'business_unit', 'account_name', 'account_id', 'region',
            'cluster_name', 'arn', 'kubernetes_version', 'platform_version', 'status',

            # Configuration
            'endpoint', 'cluster_type', 'vpc_id', 'subnet_ids_list', 'security_group_ids_list',
            'endpoint_public_access', 'endpoint_private_access', 'public_access_cidrs_list',

            # Service configuration
            'service_ipv4_cidr', 'service_ipv6_cidr', 'ip_family',

            # Features
            'logging_enabled', 'logging_types_list', 'encryption_enabled', 'encryption_key_arn',
            'role_arn', 'identity_oidc_issuer',

            # Addons and profiles
            'addons_list', 'fargate_profile_names_list', 'node_groups_count', 'fargate_profiles_count',

            # Related data
            'cluster_tags', 'node_groups', 'fargate_profiles', 'all_tags_raw',

            # Timestamps
            'created_at_aws', 'last_updated', 'created_at'
        ]

    def get_subnet_ids_list(self, obj):
        """Parse subnet IDs from JSON string"""
        try:
            if obj.subnet_ids:
                return json.loads(obj.subnet_ids)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_security_group_ids_list(self, obj):
        """Parse security group IDs from JSON string"""
        try:
            if obj.security_group_ids:
                return json.loads(obj.security_group_ids)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_public_access_cidrs_list(self, obj):
        """Parse public access CIDRs from JSON string"""
        try:
            if obj.public_access_cidrs:
                return json.loads(obj.public_access_cidrs)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_logging_types_list(self, obj):
        """Parse logging types from JSON string"""
        try:
            if obj.logging_types:
                return json.loads(obj.logging_types)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_addons_list(self, obj):
        """Parse addons from JSON string"""
        try:
            if obj.addons:
                return json.loads(obj.addons)
            return []
        except (json.JSONDecodeError, TypeError):
            return []

    def get_fargate_profile_names_list(self, obj):
        """Parse Fargate profile names from JSON string"""
        try:
            if obj.fargate_profile_names:
                return json.loads(obj.fargate_profile_names)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


# Azure Serializers

class VMTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = VMTag
        fields = ['key', 'value']


class AzureSubscriptionSerializer(serializers.ModelSerializer):
    azure_vms_count = serializers.SerializerMethodField()

    class Meta:
        model = AzureSubscription
        fields = '__all__'

    def get_azure_vms_count(self, obj):
        return obj.azure_vms.count()


class VMAgentStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = VMAgentStatus
        fields = '__all__'


class AzureVMListSerializer(serializers.ModelSerializer):
    """Simplified serializer for Azure VM list views"""
    business_unit = serializers.CharField(source='subscription.business_unit', read_only=True)
    subscription_name = serializers.CharField(source='subscription.subscription_name', read_only=True)
    subscription_id = serializers.CharField(source='subscription.subscription_id', read_only=True)
    agent_status = serializers.CharField(source='agent_status.agent_status', read_only=True)

    class Meta:
        model = AzureVM
        fields = [
            'id', 'vm_id', 'name', 'instance_type', 'state', 'location',
            'private_ip_address', 'os_type', 'os_version', 'env_tag', 'uai_tag',
            'business_unit', 'subscription_name', 'subscription_id',
            'agent_status', 'last_updated'
        ]


class AzureVMDetailedSerializer(serializers.ModelSerializer):
    """Detailed Azure VM serializer with all fields and relationships"""
    business_unit = serializers.CharField(source='subscription.business_unit', read_only=True)
    subscription_name = serializers.CharField(source='subscription.subscription_name', read_only=True)
    subscription_id = serializers.CharField(source='subscription.subscription_id', read_only=True)

    # Related objects
    vm_tags = VMTagSerializer(source='tags', many=True, read_only=True)
    agent_status = VMAgentStatusSerializer(read_only=True)

    class Meta:
        model = AzureVM
        fields = [
            # Core identification
            'business_unit', 'subscription_name', 'subscription_id',
            'vm_id', 'name', 'state', 'location', 'instance_type', 'vcpu_count', 'memory_gb',

            # Network information
            'private_ip_address', 'public_ip_address', 'fqdn', 'virtual_network', 'subnet',
            'network_security_group',

            # Storage information
            'os_disk_name', 'os_disk_size_gb', 'os_disk_type', 'data_disk_count',

            # OS information
            'os_type', 'os_name', 'os_version',

            # Azure-specific information
            'resource_group', 'availability_set', 'availability_zone',

            # Tag fields matching CSV structure
            'env_tag', 'uai_tag', 'patch_exempt', 'patch_provider', 'cto_cloud_ops_managed',
            'patch_tag', 'patch_group', 'app_env_cfg_id', 'patching_week', 'maintenance_schedule',
            'auto_shutdown_schedule', 'backup_tag',

            # Related data
            'vm_tags', 'agent_status', 'all_tags_raw',

            # Timestamps
            'last_updated', 'created_at'
        ]


class AzureInventoryRefreshLogSerializer(serializers.ModelSerializer):
    subscription_name = serializers.CharField(source='subscription.subscription_name', read_only=True)
    duration = serializers.SerializerMethodField()

    class Meta:
        model = AzureInventoryRefreshLog
        fields = '__all__'

    def get_duration(self, obj):
        if obj.completed_at and obj.started_at:
            delta = obj.completed_at - obj.started_at
            return str(delta)
        return None
