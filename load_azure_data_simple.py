#!/usr/bin/env python
"""
Simple script to load Azure sample data
"""
import os
import sys
import django
import csv

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AzureVM, AzureSubscription, VMTag, VMAgentStatus

def load_azure_data():
    print("🚀 Starting Azure data loading...")
    
    # Clear existing data
    print("🧹 Clearing existing data...")
    AzureVM.objects.all().delete()
    AzureSubscription.objects.all().delete()
    VMTag.objects.all().delete()
    print("✅ Existing data cleared")
    
    # Load data from CSV
    csv_file = 'azure_sample.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV file {csv_file} not found")
        return
    
    print(f"📁 Loading data from {csv_file}...")
    
    subscriptions_created = 0
    vms_created = 0
    tags_created = 0
    
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row_num, row in enumerate(reader, start=2):
            try:
                # Extract data
                subscription_name = row.get('SubscriptionName', '').strip()
                business_unit = row.get('BU', '').strip()
                vm_name = row.get('VMName', '').strip()
                instance_type = row.get('InstanceType', '').strip()
                os_version = row.get('OSVersion', '').strip()
                status = row.get('Status', '').strip()
                private_ip = row.get('PrivateIpAddress', '').strip()
                env = row.get('Env', '').strip()
                uai = row.get('UAI', '').strip()
                patch_exempt = row.get('PatchExempt', '').strip()
                patch_provider = row.get('Patch_Provider', '').strip()
                cto_cloud_ops_managed = row.get('CTOCloudOpsManaged', '').strip()
                patch = row.get('Patch', '').strip()
                patch_group = row.get('Patch_Group', '').strip()
                app_env_cfg_id = row.get('AppEnvCfgID', '').strip()
                patching_week = row.get('PatchingWeek', '').strip()
                maintenance_schedule = row.get('MaintenanceSchedule', '').strip()
                auto_shutdown_schedule = row.get('AutoShutdownSchedule', '').strip()
                backup = row.get('Backup', '').strip()

                # Skip rows with missing essential data
                if not subscription_name or not vm_name:
                    continue

                # Create or get Azure subscription
                subscription, created = AzureSubscription.objects.get_or_create(
                    subscription_name=subscription_name,
                    defaults={
                        'subscription_id': f'sub-{subscription_name.lower().replace(" ", "-").replace("_", "-")}',
                        'business_unit': business_unit,
                        'location': 'East US',
                        'is_active': True
                    }
                )
                if created:
                    subscriptions_created += 1

                # Generate VM ID
                vm_id = f'/subscriptions/{subscription.subscription_id}/resourceGroups/rg-{vm_name.lower()}/providers/Microsoft.Compute/virtualMachines/{vm_name}'

                # Determine OS type
                os_type = 'Unknown'
                if os_version:
                    if 'Windows' in os_version:
                        os_type = 'Windows'
                    elif any(os in os_version.lower() for os in ['linux', 'redhat', 'ubuntu', 'oracle', 'qualys']):
                        os_type = 'Linux'

                # Create Azure VM
                vm, created = AzureVM.objects.update_or_create(
                    subscription=subscription,
                    name=vm_name,
                    defaults={
                        'vm_id': vm_id,
                        'state': status if status else 'VM unknown',
                        'location': 'East US',
                        'instance_type': instance_type,
                        'private_ip_address': private_ip,
                        'os_type': os_type,
                        'os_version': os_version,
                        'resource_group': f'rg-{vm_name.lower()}',
                        
                        # Tag fields from CSV
                        'env_tag': env,
                        'uai_tag': uai,
                        'patch_exempt': patch_exempt,
                        'patch_provider': patch_provider,
                        'cto_cloud_ops_managed': cto_cloud_ops_managed,
                        'patch_tag': patch,
                        'patch_group': patch_group,
                        'app_env_cfg_id': app_env_cfg_id,
                        'patching_week': patching_week,
                        'maintenance_schedule': maintenance_schedule,
                        'auto_shutdown_schedule': auto_shutdown_schedule,
                        'backup_tag': backup,
                        
                        # Raw tags for backup
                        'all_tags_raw': f'Env={env}, UAI={uai}, PatchExempt={patch_exempt}'
                    }
                )
                if created:
                    vms_created += 1

                # Create flattened tags
                tag_data = [
                    ('Env', env),
                    ('UAI', uai),
                    ('PatchExempt', patch_exempt),
                    ('Patch_Provider', patch_provider),
                    ('CTOCloudOpsManaged', cto_cloud_ops_managed),
                ]

                # Clear existing tags for this VM
                vm.tags.clear()

                # Add tags with values
                for key, value in tag_data:
                    if value:  # Only add tags with values
                        tag, tag_created = VMTag.objects.get_or_create(
                            key=key,
                            value=value
                        )
                        vm.tags.add(tag)
                        if tag_created:
                            tags_created += 1

                # Create VM Agent Status
                VMAgentStatus.objects.update_or_create(
                    vm=vm,
                    defaults={
                        'agent_status': 'Ready' if status == 'VM running' else 'Unknown',
                        'agent_version': '2.7.41491.1010' if os_type == 'Windows' else '2.2.54.2',
                        'extensions_count': 2 if status == 'VM running' else 0,
                        'extensions_status': '[]',
                        'boot_diagnostics_enabled': True if status == 'VM running' else False
                    }
                )

                if row_num % 50 == 0:
                    print(f"📊 Processed {row_num-1} rows...")

            except Exception as e:
                print(f"❌ Error processing row {row_num}: {str(e)}")
                continue

    # Print summary
    print(f"\n📈 Summary:")
    print(f"   - Subscriptions created: {subscriptions_created}")
    print(f"   - VMs created: {vms_created}")
    print(f"   - Tags created: {tags_created}")
    print(f"   - Total VMs in database: {AzureVM.objects.count()}")
    print(f"   - Total subscriptions in database: {AzureSubscription.objects.count()}")
    
    # Show sample data
    if AzureVM.objects.count() > 0:
        print(f"\n🖥️  Sample VMs:")
        for vm in AzureVM.objects.all()[:5]:
            print(f"   - {vm.name} ({vm.instance_type}) - {vm.env_tag} - {vm.uai_tag}")
    
    print(f"\n🎉 Azure data loading completed successfully!")

if __name__ == '__main__':
    load_azure_data()
