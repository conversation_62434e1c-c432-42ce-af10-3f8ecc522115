{% extends 'inventory/base.html' %}
{% load static %}

{% block title %}{{ user_obj.get_full_name|default:user_obj.username }} - User Details - Cloud Operations Central{% endblock %}

{% block extra_css %}
<style>
    .user-header {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }

    .user-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50px, -50px);
    }

    .user-avatar-large {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 2rem;
        border: 3px solid rgba(255, 255, 255, 0.3);
        margin-right: 20px;
    }

    .user-info h2 {
        margin-bottom: 8px;
        font-weight: 600;
    }

    .user-info p {
        margin-bottom: 4px;
        opacity: 0.9;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 10px;
    }

    .status-active {
        background: rgba(39, 174, 96, 0.2);
        color: #27ae60;
        border: 1px solid rgba(39, 174, 96, 0.3);
    }

    .status-inactive {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.3);
    }

    .status-pending {
        background: rgba(243, 156, 18, 0.2);
        color: #f39c12;
        border: 1px solid rgba(243, 156, 18, 0.3);
    }

    .info-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f3f4;
    }

    .info-card h5 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .info-card h5 i {
        color: #3498db;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 500;
    }

    .role-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .role-admin {
        background: #e74c3c;
        color: white;
    }

    .role-automation_user {
        background: #3498db;
        color: white;
    }

    .role-reader {
        background: #95a5a6;
        color: white;
    }

    .permissions-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .permissions-list li {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .permissions-list li:last-child {
        border-bottom: none;
    }

    .permissions-list i {
        color: #27ae60;
        font-size: 0.875rem;
    }

    .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        flex-shrink: 0;
        margin-top: 4px;
    }

    .activity-login { background: #e8f5e8; color: #27ae60; }
    .activity-logout { background: #fff3cd; color: #856404; }
    .activity-manage_users { background: #d1ecf1; color: #0c5460; }
    .activity-view_inventory { background: #f8d7da; color: #721c24; }

    .activity-content {
        flex: 1;
    }

    .activity-description {
        color: #2c3e50;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .activity-meta {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .session-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 8px;
        border-left: 4px solid #3498db;
    }

    .session-info {
        flex: 1;
    }

    .session-device {
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .session-details {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .session-status {
        text-align: right;
    }

    .btn-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .btn-actions .btn {
        font-size: 0.875rem;
        padding: 8px 16px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f3f4;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3498db, #5dade2);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- User Header -->
    <div class="user-header">
        <div class="d-flex align-items-center">
            <div class="user-avatar-large">
                {{ user_obj.first_name|first|default:user_obj.username|first|upper }}
            </div>
            <div class="user-info flex-grow-1">
                <h2>{{ user_obj.get_full_name|default:user_obj.username }}</h2>
                <p><i class="fas fa-envelope me-2"></i>{{ user_obj.email }}</p>
                {% if user_obj.employee_id %}
                    <p><i class="fas fa-id-badge me-2"></i>Employee ID: {{ user_obj.employee_id }}</p>
                {% endif %}
                {% if user_obj.user_role %}
                    <div class="role-badge role-{{ user_obj.user_role.name }}">
                        <i class="fas fa-shield-alt"></i>{{ user_obj.user_role.display_name }}
                    </div>
                {% endif %}
                <div class="status-badge {% if user_obj.is_active and user_obj.is_approved %}status-active{% elif not user_obj.is_approved %}status-pending{% else %}status-inactive{% endif %}">
                    <i class="fas fa-circle"></i>
                    {% if user_obj.is_active and user_obj.is_approved %}
                        Active
                    {% elif not user_obj.is_approved %}
                        Pending Approval
                    {% else %}
                        Inactive
                    {% endif %}
                </div>
            </div>
            <div class="btn-actions">
                <a href="{% url 'accounts:user_edit' user_obj.pk %}" class="btn btn-light">
                    <i class="fas fa-edit me-1"></i>Edit User
                </a>
                {% if not user_obj.is_approved %}
                <a href="{% url 'accounts:user_approve' user_obj.pk %}" class="btn btn-success">
                    <i class="fas fa-check me-1"></i>Approve
                </a>
                {% endif %}
                <a href="{% url 'accounts:user_toggle_status' user_obj.pk %}" 
                   class="btn btn-{% if user_obj.is_active %}warning{% else %}success{% endif %}"
                   onclick="return confirm('Are you sure you want to {% if user_obj.is_active %}deactivate{% else %}activate{% endif %} this user?')">
                    <i class="fas fa-{% if user_obj.is_active %}pause{% else %}play{% endif %} me-1"></i>
                    {% if user_obj.is_active %}Deactivate{% else %}Activate{% endif %}
                </a>
                <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ user_obj.login_count|default:0 }}</div>
            <div class="stat-label">Total Logins</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ sessions.count }}</div>
            <div class="stat-label">Active Sessions</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ activities.count }}</div>
            <div class="stat-label">Recent Activities</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% if user_obj.last_login %}
                    {{ user_obj.last_login|timesince|truncatewords:2 }}
                {% else %}
                    Never
                {% endif %}
            </div>
            <div class="stat-label">Last Login</div>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-user"></i>Personal Information</h5>
                <div class="info-row">
                    <span class="info-label">Full Name</span>
                    <span class="info-value">{{ user_obj.get_full_name|default:"Not provided" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Username</span>
                    <span class="info-value">{{ user_obj.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email</span>
                    <span class="info-value">{{ user_obj.email }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Employee ID</span>
                    <span class="info-value">{{ user_obj.employee_id|default:"Not provided" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Phone Number</span>
                    <span class="info-value">{{ user_obj.phone_number|default:"Not provided" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Department</span>
                    <span class="info-value">{{ user_obj.department|default:"Not provided" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Business Unit</span>
                    <span class="info-value">{{ user_obj.business_unit|default:"Not provided" }}</span>
                </div>
            </div>

            <div class="info-card">
                <h5><i class="fas fa-shield-alt"></i>Permissions</h5>
                {% if permissions %}
                    <ul class="permissions-list">
                        {% for permission in permissions %}
                            <li>
                                <i class="fas fa-check"></i>
                                <span>{{ permission }}</span>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <p class="text-muted">No specific permissions assigned.</p>
                {% endif %}
            </div>
        </div>

        <!-- Account Details and Activity -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-cog"></i>Account Details</h5>
                <div class="info-row">
                    <span class="info-label">Account Status</span>
                    <span class="info-value">
                        {% if user_obj.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Approval Status</span>
                    <span class="info-value">
                        {% if user_obj.is_approved %}
                            <span class="badge bg-success">Approved</span>
                        {% else %}
                            <span class="badge bg-warning">Pending</span>
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">User Role</span>
                    <span class="info-value">
                        {% if user_obj.user_role %}
                            <span class="role-badge role-{{ user_obj.user_role.name }}">
                                {{ user_obj.user_role.display_name }}
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">No Role</span>
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date Joined</span>
                    <span class="info-value">{{ user_obj.date_joined|date:"M d, Y H:i" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Last Updated</span>
                    <span class="info-value">{{ user_obj.updated_at|date:"M d, Y H:i" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Last Login</span>
                    <span class="info-value">
                        {% if user_obj.last_login %}
                            {{ user_obj.last_login|date:"M d, Y H:i" }}
                        {% else %}
                            Never
                        {% endif %}
                    </span>
                </div>
                {% if user_obj.approved_by %}
                <div class="info-row">
                    <span class="info-label">Approved By</span>
                    <span class="info-value">{{ user_obj.approved_by.get_full_name|default:user_obj.approved_by.username }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Activity and Sessions -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-history"></i>Recent Activity</h5>
                {% if activities %}
                    {% for activity in activities %}
                        <div class="activity-item">
                            <div class="activity-icon activity-{{ activity.action }}">
                                {% if activity.action == 'login' %}
                                    <i class="fas fa-sign-in-alt"></i>
                                {% elif activity.action == 'logout' %}
                                    <i class="fas fa-sign-out-alt"></i>
                                {% elif activity.action == 'manage_users' %}
                                    <i class="fas fa-users-cog"></i>
                                {% elif activity.action == 'view_inventory' %}
                                    <i class="fas fa-eye"></i>
                                {% else %}
                                    <i class="fas fa-circle"></i>
                                {% endif %}
                            </div>
                            <div class="activity-content">
                                <div class="activity-description">{{ activity.description }}</div>
                                <div class="activity-meta">
                                    <i class="fas fa-clock me-1"></i>{{ activity.timestamp|timesince }} ago
                                    {% if activity.ip_address %}
                                        <span class="ms-2">
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ activity.ip_address }}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No recent activity recorded.</p>
                {% endif %}
            </div>
        </div>

        <!-- Active Sessions -->
        <div class="col-lg-6">
            <div class="info-card">
                <h5><i class="fas fa-desktop"></i>Active Sessions</h5>
                {% if sessions %}
                    {% for session in sessions %}
                        <div class="session-item">
                            <div class="session-info">
                                <div class="session-device">
                                    <i class="fas fa-{% if 'Mobile' in session.user_agent %}mobile-alt{% elif 'Tablet' in session.user_agent %}tablet-alt{% else %}desktop{% endif %} me-2"></i>
                                    {% if session.user_agent %}
                                        {{ session.user_agent|truncatechars:50 }}
                                    {% else %}
                                        Unknown Device
                                    {% endif %}
                                </div>
                                <div class="session-details">
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ session.ip_address|default:"Unknown IP" }}</span>
                                    <span class="ms-3"><i class="fas fa-clock me-1"></i>{{ session.login_time|timesince }} ago</span>
                                </div>
                            </div>
                            <div class="session-status">
                                {% if session.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Expired</span>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No active sessions found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
