{% extends 'inventory/base.html' %}
{% load static %}

{% block title %}{{ title }} - Cloud Operations Central{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .form-section {
        background: white;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .form-section h5 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .form-section h5 i {
        color: #3498db;
        margin-right: 8px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 6px;
    }

    .form-control, .form-select {
        border: 1px solid #e1e5e9;
        border-radius: 6px;
        padding: 10px 12px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
    }

    .form-text {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 4px;
    }

    .required-field::after {
        content: " *";
        color: #e74c3c;
    }

    .role-info {
        background: #f8f9fa;
        border-left: 4px solid #3498db;
        padding: 12px 16px;
        margin-top: 10px;
        border-radius: 0 6px 6px 0;
    }

    .role-info h6 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 6px;
    }

    .role-info p {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 0;
    }

    .btn-group-form {
        background: white;
        border-radius: 8px;
        padding: 20px 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .error-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .error-list li {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 4px;
    }

    .password-strength {
        margin-top: 8px;
    }

    .strength-bar {
        height: 4px;
        border-radius: 2px;
        background: #e9ecef;
        overflow: hidden;
        margin-bottom: 4px;
    }

    .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        width: 0%;
    }

    .strength-weak { background-color: #e74c3c; }
    .strength-fair { background-color: #f39c12; }
    .strength-good { background-color: #f1c40f; }
    .strength-strong { background-color: #27ae60; }

    .strength-text {
        font-size: 0.75rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="form-container">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-{% if object %}user-edit{% else %}user-plus{% endif %} me-2"></i>{{ title }}
                </h2>
                <p class="text-muted mb-0">
                    {% if object %}
                        Update user information, role, and permissions
                    {% else %}
                        Create a new user account with appropriate role and permissions
                    {% endif %}
                </p>
            </div>
            <div>
                <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>

        <form method="post" id="userForm">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="form-section">
                <h5><i class="fas fa-user"></i>Basic Information</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.username.id_for_label }}" class="form-label required-field">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <ul class="error-list">
                                    {% for error in form.username.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Unique identifier for login. Cannot be changed after creation.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}" class="form-label required-field">Email Address</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <ul class="error-list">
                                    {% for error in form.email.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Primary email address for notifications and communication.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label required-field">First Name</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <ul class="error-list">
                                    {% for error in form.first_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label required-field">Last Name</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <ul class="error-list">
                                    {% for error in form.last_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Section (only for new users) -->
            {% if not object %}
            <div class="form-section">
                <h5><i class="fas fa-lock"></i>Password</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.password1.id_for_label }}" class="form-label required-field">Password</label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <ul class="error-list">
                                    {% for error in form.password1.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="password-strength" id="passwordStrength" style="display: none;">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strengthFill"></div>
                                </div>
                                <div class="strength-text" id="strengthText"></div>
                            </div>
                            <div class="form-text">
                                Password must be at least 8 characters long and contain a mix of letters, numbers, and symbols.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.password2.id_for_label }}" class="form-label required-field">Confirm Password</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <ul class="error-list">
                                    {% for error in form.password2.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Re-enter the password to confirm.</div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Organization Information -->
            <div class="form-section">
                <h5><i class="fas fa-building"></i>Organization Information</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.employee_id.id_for_label }}" class="form-label">Employee ID</label>
                            {{ form.employee_id }}
                            {% if form.employee_id.errors %}
                                <ul class="error-list">
                                    {% for error in form.employee_id.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Optional employee identifier.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <ul class="error-list">
                                    {% for error in form.phone_number.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Format: +1234567890</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.department.id_for_label }}" class="form-label">Department</label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <ul class="error-list">
                                    {% for error in form.department.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.business_unit.id_for_label }}" class="form-label">Business Unit</label>
                            {{ form.business_unit }}
                            {% if form.business_unit.errors %}
                                <ul class="error-list">
                                    {% for error in form.business_unit.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role and Permissions -->
            <div class="form-section">
                <h5><i class="fas fa-shield-alt"></i>Role and Permissions</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.user_role.id_for_label }}" class="form-label required-field">User Role</label>
                            {{ form.user_role }}
                            {% if form.user_role.errors %}
                                <ul class="error-list">
                                    {% for error in form.user_role.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="form-text">Select the appropriate role for this user.</div>
                        </div>
                        
                        <!-- Role Information Display -->
                        <div id="roleInfo" class="role-info" style="display: none;">
                            <h6 id="roleTitle"></h6>
                            <p id="roleDescription"></p>
                        </div>
                    </div>
                    
                    {% if object %}
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Account Status</label>
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Account is active
                                </label>
                            </div>
                            <div class="form-check">
                                {{ form.is_approved }}
                                <label class="form-check-label" for="{{ form.is_approved.id_for_label }}">
                                    Account is approved
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <ul class="error-list">
                                    {% for error in form.is_active.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            {% if form.is_approved.errors %}
                                <ul class="error-list">
                                    {% for error in form.is_approved.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Form Actions -->
            <div class="btn-group-form">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {% if object %}
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Last updated: {{ object.updated_at|date:"M d, Y H:i" }}
                            </small>
                        {% endif %}
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if object %}Update User{% else %}Create User{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Role information data
    const roleInfo = {
        {% for role in roles %}
        '{{ role.name }}': {
            title: '{{ role.display_name }}',
            description: '{{ role.description|escapejs }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };

    // Role selection handler
    const roleSelect = document.getElementById('{{ form.user_role.id_for_label }}');
    const roleInfoDiv = document.getElementById('roleInfo');
    const roleTitle = document.getElementById('roleTitle');
    const roleDescription = document.getElementById('roleDescription');

    if (roleSelect) {
        roleSelect.addEventListener('change', function() {
            const selectedRole = this.value;
            if (selectedRole && roleInfo[selectedRole]) {
                roleTitle.textContent = roleInfo[selectedRole].title;
                roleDescription.textContent = roleInfo[selectedRole].description;
                roleInfoDiv.style.display = 'block';
            } else {
                roleInfoDiv.style.display = 'none';
            }
        });

        // Trigger on page load if role is already selected
        if (roleSelect.value) {
            roleSelect.dispatchEvent(new Event('change'));
        }
    }

    // Password strength checker (only for new users)
    const password1 = document.getElementById('{{ form.password1.id_for_label }}');
    if (password1) {
        const strengthDiv = document.getElementById('passwordStrength');
        const strengthFill = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');

        password1.addEventListener('input', function() {
            const password = this.value;
            if (password.length === 0) {
                strengthDiv.style.display = 'none';
                return;
            }

            strengthDiv.style.display = 'block';
            const strength = calculatePasswordStrength(password);
            updatePasswordStrength(strength, strengthFill, strengthText);
        });
    }

    // Form validation
    const form = document.getElementById('userForm');
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });

    function calculatePasswordStrength(password) {
        let score = 0;
        let feedback = [];

        // Length check
        if (password.length >= 8) score += 1;
        else feedback.push('At least 8 characters');

        // Character variety checks
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('Lowercase letters');

        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('Uppercase letters');

        if (/[0-9]/.test(password)) score += 1;
        else feedback.push('Numbers');

        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        else feedback.push('Special characters');

        return {
            score: score,
            feedback: feedback,
            level: score <= 1 ? 'weak' : score <= 2 ? 'fair' : score <= 3 ? 'good' : 'strong'
        };
    }

    function updatePasswordStrength(strength, fillElement, textElement) {
        const percentage = (strength.score / 5) * 100;
        fillElement.style.width = percentage + '%';
        fillElement.className = 'strength-fill strength-' + strength.level;

        let text = 'Password strength: ' + strength.level.charAt(0).toUpperCase() + strength.level.slice(1);
        if (strength.feedback.length > 0) {
            text += ' (Missing: ' + strength.feedback.join(', ') + ')';
        }
        textElement.textContent = text;
        textElement.className = 'strength-text text-' +
            (strength.level === 'weak' ? 'danger' :
             strength.level === 'fair' ? 'warning' :
             strength.level === 'good' ? 'info' : 'success');
    }

    function validateForm() {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        // Email validation
        const emailField = document.getElementById('{{ form.email.id_for_label }}');
        if (emailField && emailField.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailField.value)) {
                emailField.classList.add('is-invalid');
                isValid = false;
            } else {
                emailField.classList.remove('is-invalid');
            }
        }

        // Password confirmation (for new users)
        const password1 = document.getElementById('{{ form.password1.id_for_label }}');
        const password2 = document.getElementById('{{ form.password2.id_for_label }}');
        if (password1 && password2) {
            if (password1.value !== password2.value) {
                password2.classList.add('is-invalid');
                isValid = false;
            } else {
                password2.classList.remove('is-invalid');
            }
        }

        if (!isValid) {
            showAlert('Please correct the errors in the form before submitting.', 'danger');
        }

        return isValid;
    }

    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>
{% endblock %}
