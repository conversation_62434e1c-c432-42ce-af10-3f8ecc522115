{% extends 'inventory/base.html' %}
{% load static %}

{% block title %}User Management - Cloud Operations Central{% endblock %}

{% block extra_css %}
<style>
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3498db, #2980b9);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .user-status {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }

    .status-active { background-color: #27ae60; }
    .status-inactive { background-color: #e74c3c; }
    .status-pending { background-color: #f39c12; }

    .role-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-weight: 500;
    }

    .role-admin { background-color: #e74c3c; color: white; }
    .role-automation_user { background-color: #3498db; color: white; }
    .role-reader { background-color: #95a5a6; color: white; }

    .bulk-actions {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #e9ecef;
    }

    .user-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        margin: 0 2px;
    }

    .search-filters {
        background: white;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .stats-row {
        margin-bottom: 20px;
    }

    .user-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #e9ecef;
        font-weight: 600;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 12px 8px;
    }

    .table td {
        padding: 12px 8px;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .pagination-container {
        background: white;
        border-radius: 8px;
        padding: 16px;
        margin-top: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-users-cog me-2"></i>User Management</h2>
            <p class="text-muted mb-0">Manage user accounts, roles, and permissions</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-secondary" onclick="exportUsers()">
                <i class="fas fa-download me-1"></i>Export CSV
            </button>
            <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Create User
            </a>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row stats-row">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="card-body">
                    <h3>{{ users.count }}</h3>
                    <h6>Total Users</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-success">
                <div class="card-body">
                    <h3>{{ active_users_count }}</h3>
                    <h6>Active Users</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-warning">
                <div class="card-body">
                    <h3>{{ pending_users_count }}</h3>
                    <h6>Pending Approval</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-info">
                <div class="card-body">
                    <h3>{{ admin_users_count }}</h3>
                    <h6>Admin Users</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request.GET.search }}" placeholder="Username, name, email...">
            </div>
            <div class="col-md-2">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="">All Roles</option>
                    {% for role in roles %}
                        <option value="{{ role.name }}" {% if request.GET.role == role.name %}selected{% endif %}>
                            {{ role.display_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending Approval</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="business_unit" class="form-label">Business Unit</label>
                <select class="form-select" id="business_unit" name="business_unit">
                    <option value="">All BUs</option>
                    {% for bu in business_units %}
                        <option value="{{ bu }}" {% if request.GET.business_unit == bu %}selected{% endif %}>{{ bu }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                    <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    {% if users %}
    <div class="bulk-actions" style="display: none;" id="bulkActions">
        <form method="post" action="{% url 'accounts:bulk_user_actions' %}" id="bulkForm">
            {% csrf_token %}
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Bulk Actions</label>
                    <select class="form-select" name="action" required>
                        <option value="">Select Action</option>
                        <option value="activate">Activate Users</option>
                        <option value="deactivate">Deactivate Users</option>
                        <option value="approve">Approve Users</option>
                        <option value="change_role">Change Role</option>
                    </select>
                </div>
                <div class="col-md-3" id="roleSelection" style="display: none;">
                    <label class="form-label">New Role</label>
                    <select class="form-select" name="new_role">
                        {% for role in roles %}
                            <option value="{{ role.name }}">{{ role.display_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-cogs me-1"></i>Apply to Selected
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                        Cancel
                    </button>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">
                        <span id="selectedCount">0</span> users selected
                    </small>
                </div>
            </div>
            <input type="hidden" name="user_ids" id="selectedUserIds">
        </form>
    </div>
    {% endif %}

    <!-- Users Table -->
    <div class="user-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>User</th>
                        <th>Role</th>
                        <th>Business Unit</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Created</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_obj in users %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input user-checkbox" 
                                   value="{{ user_obj.id }}" data-user-id="{{ user_obj.id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {{ user_obj.first_name|first|default:user_obj.username|first|upper }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ user_obj.get_full_name|default:user_obj.username }}</div>
                                    <small class="text-muted">{{ user_obj.email }}</small>
                                    {% if user_obj.employee_id %}
                                        <br><small class="text-muted">ID: {{ user_obj.employee_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if user_obj.user_role %}
                                <span class="role-badge role-{{ user_obj.user_role.name }}">
                                    {{ user_obj.user_role.display_name }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">No Role</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user_obj.business_unit %}
                                <span class="badge bg-light text-dark">{{ user_obj.business_unit }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="user-status">
                                {% if user_obj.is_active and user_obj.is_approved %}
                                    <span class="status-indicator status-active"></span>
                                    <span class="text-success">Active</span>
                                {% elif not user_obj.is_approved %}
                                    <span class="status-indicator status-pending"></span>
                                    <span class="text-warning">Pending</span>
                                {% else %}
                                    <span class="status-indicator status-inactive"></span>
                                    <span class="text-danger">Inactive</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if user_obj.last_login %}
                                <small title="{{ user_obj.last_login }}">
                                    {{ user_obj.last_login|timesince }} ago
                                </small>
                            {% else %}
                                <span class="text-muted">Never</span>
                            {% endif %}
                        </td>
                        <td>
                            <small title="{{ user_obj.created_at }}">
                                {{ user_obj.created_at|date:"M d, Y" }}
                            </small>
                        </td>
                        <td>
                            <div class="user-actions">
                                <a href="{% url 'accounts:user_detail' user_obj.pk %}" 
                                   class="btn btn-outline-info" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'accounts:user_edit' user_obj.pk %}" 
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if not user_obj.is_approved %}
                                <a href="{% url 'accounts:user_approve' user_obj.pk %}" 
                                   class="btn btn-outline-success" title="Approve">
                                    <i class="fas fa-check"></i>
                                </a>
                                {% endif %}
                                <a href="{% url 'accounts:user_toggle_status' user_obj.pk %}" 
                                   class="btn btn-outline-warning" 
                                   title="{% if user_obj.is_active %}Deactivate{% else %}Activate{% endif %}">
                                    <i class="fas fa-{% if user_obj.is_active %}pause{% else %}play{% endif %}"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>No users found matching your criteria.</p>
                                <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Create First User
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="pagination-container">
        <nav aria-label="User pagination">
            <ul class="pagination justify-content-center mb-0">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.business_unit %}business_unit={{ request.GET.business_unit }}&{% endif %}page=1">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.business_unit %}business_unit={{ request.GET.business_unit }}&{% endif %}page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.business_unit %}business_unit={{ request.GET.business_unit }}&{% endif %}page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.role %}role={{ request.GET.role }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}{% if request.GET.business_unit %}business_unit={{ request.GET.business_unit }}&{% endif %}page={{ page_obj.paginator.num_pages }}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    const selectedUserIds = document.getElementById('selectedUserIds');
    const bulkActionSelect = document.querySelector('select[name="action"]');
    const roleSelection = document.getElementById('roleSelection');

    // Handle select all
    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Handle individual checkboxes
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAll();
            updateBulkActions();
        });
    });

    // Show/hide role selection based on action
    if (bulkActionSelect) {
        bulkActionSelect.addEventListener('change', function() {
            if (this.value === 'change_role') {
                roleSelection.style.display = 'block';
            } else {
                roleSelection.style.display = 'none';
            }
        });
    }

    function updateSelectAll() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        selectAllCheckbox.checked = checkedBoxes.length === userCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < userCheckboxes.length;
    }

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const count = checkedBoxes.length;

        if (count > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = count;

            // Update hidden input with selected user IDs
            const userIds = Array.from(checkedBoxes).map(cb => cb.value);
            selectedUserIds.value = userIds.join(',');
        } else {
            bulkActions.style.display = 'none';
        }
    }

    // Handle bulk form submission
    const bulkForm = document.getElementById('bulkForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const action = bulkActionSelect.value;
            const count = document.querySelectorAll('.user-checkbox:checked').length;

            if (!action) {
                e.preventDefault();
                showAlert('Please select an action to perform.', 'warning');
                return;
            }

            const actionNames = {
                'activate': 'activate',
                'deactivate': 'deactivate',
                'approve': 'approve',
                'change_role': 'change role for'
            };

            const actionName = actionNames[action] || action;
            const message = `Are you sure you want to ${actionName} ${count} user(s)?`;

            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    }
});

// Clear selection function
function clearSelection() {
    document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAll').checked = false;
    document.getElementById('bulkActions').style.display = 'none';
}

// Export users function
function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');

    const exportUrl = window.location.pathname + '?' + params.toString();
    window.location.href = exportUrl;
}

// Auto-submit search form with debounce
const searchInput = document.getElementById('search');
if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Auto-submit form after 500ms of no typing
            // Uncomment if you want auto-search
            // this.form.submit();
        }, 500);
    });
}

// Initialize tooltips for action buttons
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Handle status toggle with confirmation
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href*="toggle-status"]')) {
        e.preventDefault();
        const link = e.target.closest('a');
        const isActive = link.title.includes('Deactivate');
        const action = isActive ? 'deactivate' : 'activate';

        if (confirm(`Are you sure you want to ${action} this user?`)) {
            window.location.href = link.href;
        }
    }
});
</script>
{% endblock %}
