{% extends 'inventory/base.html' %}
{% load static %}

{% block title %}Azure Virtual Machines - Cloud Operations Central{% endblock %}

{% block extra_css %}
<style>
    .filter-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .vm-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        transition: box-shadow 0.2s;
    }
    
    .vm-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .vm-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85em;
        font-weight: 500;
    }
    
    .status-running { background: #d4edda; color: #155724; }
    .status-stopped { background: #f8d7da; color: #721c24; }
    .status-deallocated { background: #fff3cd; color: #856404; }
    .status-unknown { background: #e2e3e5; color: #383d41; }
    
    .vm-size-badge {
        background: #e3f2fd;
        color: #1565c0;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8em;
    }
    
    .tag-badge {
        background: #f0f0f0;
        color: #333;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.75em;
        margin-right: 5px;
        margin-bottom: 3px;
        display: inline-block;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        height: 50px;
        align-items: center;
        align-content: center;
    }
    
    .table-compact {
        font-size: 0.9em;
    }
    
    .table-compact th,
    .table-compact td {
        padding: 8px;
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h6><i class="fas fa-server text-primary"></i> Azure Virtual Machines</h6>
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshInventory()">
                        <i class="fas fa-sync-alt"></i> Refresh Inventory
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="exportData()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <span><b>Total VMs</b></span> <b>:</b>
                        <span id="total-vms"><b>-</b></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span><b>Running VMs</b></span> <b>:</b>
                        <span id="running-vms"><b>-</b></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span><b>VM Agent Ready</b></span> <b>:</b>
                        <span id="agent-ready"><b>-</b></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <span><b>Subscriptions</b></span> <b>:</b>
                        <span id="total-subscriptions"><b>-</b></span>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control form-control-sm" id="search-input" 
                               placeholder="Search VMs...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Business Unit</label>
                        <select class="form-select form-select-sm" id="business-unit-filter">
                            <option value="">All Business Units</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Subscription</label>
                        <select class="form-select form-select-sm" id="subscription-filter">
                            <option value="">All Subscriptions</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">State</label>
                        <select class="form-select form-select-sm" id="state-filter">
                            <option value="">All States</option>
                            <option value="VM running">Running</option>
                            <option value="VM stopped">Stopped</option>
                            <option value="VM deallocated">Deallocated</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Location</label>
                        <select class="form-select form-select-sm" id="location-filter">
                            <option value="">All Locations</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-outline-secondary btn-sm d-block" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- VMs Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table-sm table-compact table-hover" id="vms-table">
                            <thead class="table-light">
                                <tr>
                                    <th>VM Name</th>
                                    <th>State</th>
                                    <th>Instance Type</th>
                                    <th>Private IP</th>
                                    <th>OS Version</th>
                                    <th>Environment</th>
                                    <th>UAI</th>
                                    <th>CTO Managed</th>
                                    <th>Agent Status</th>
                                    <th>Subscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="vms-tbody">
                                <!-- VMs will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="VMs pagination">
                        <ul class="pagination pagination-sm justify-content-center" id="pagination">
                            <!-- Pagination will be loaded here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- VM Details Modal -->
<div class="modal fade" id="vmDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title">VM Details</h6>
                <button type="button" class="btn-close btn-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-3" id="vm-details-content">
                <!-- VM details will be loaded here -->
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentFilters = {};

// Load VMs data
function loadVMs(page = 1) {
    const params = new URLSearchParams({
        page: page,
        ...currentFilters
    });
    
    fetch(`/api/azure-vms/?${params}`)
        .then(response => response.json())
        .then(data => {
            updateVMsTable(data.results);
            updatePagination(data);
            updateStats();
        })
        .catch(error => {
            console.error('Error loading VMs:', error);
            showNotification('Error loading Azure VMs', 'error');
        });
}

// Update VMs table
function updateVMsTable(vms) {
    const tbody = document.getElementById('vms-tbody');
    tbody.innerHTML = '';

    vms.forEach(vm => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${vm.name || vm.vm_id}</strong><br>
                <small class="text-muted">${vm.vm_id}</small>
            </td>
            <td><span class="vm-status status-${vm.state.toLowerCase().replace(' ', '-')}">${vm.state}</span></td>
            <td><span class="vm-size-badge">${vm.instance_type}</span></td>
            <td>${vm.private_ip_address || '-'}</td>
            <td><small>${vm.os_version || '-'}</small></td>
            <td>${vm.env_tag || '-'}</td>
            <td>${vm.uai_tag || '-'}</td>
            <td>${vm.cto_cloud_ops_managed || '-'}</td>
            <td><span class="badge bg-${getAgentColor(vm.agent_status)}">${vm.agent_status || 'Unknown'}</span></td>
            <td>
                <strong>${vm.business_unit}</strong><br>
                <small class="text-muted">${vm.subscription_name}</small>
            </td>
            <td>
                <button class="btn btn-outline-primary btn-sm" onclick="showVMDetails(${vm.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Update pagination
function updatePagination(data) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (data.count <= 20) return; // Don't show pagination if all items fit on one page

    const totalPages = Math.ceil(data.count / 20);
    const currentPage = Math.ceil((data.previous ? data.previous.split('page=')[1] : 0) + 1) || 1;

    // Previous button
    if (data.previous) {
        const prevLi = document.createElement('li');
        prevLi.className = 'page-item';
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadVMs(${currentPage - 1})">Previous</a>`;
        pagination.appendChild(prevLi);
    }

    // Page numbers
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadVMs(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    if (data.next) {
        const nextLi = document.createElement('li');
        nextLi.className = 'page-item';
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadVMs(${currentPage + 1})">Next</a>`;
        pagination.appendChild(nextLi);
    }
}

// Show VM details
function showVMDetails(vmId) {
    fetch(`/api/azure-vms/${vmId}/`)
        .then(response => response.json())
        .then(vm => {
            const content = document.getElementById('vm-details-content');
            content.innerHTML = `
                <div class="row g-2">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header py-1"><small class="fw-bold">Basic Information</small></div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                    <tr><td class="fw-bold" style="width: 40%;">VM ID:</td><td><code>${vm.vm_id}</code></td></tr>
                                    <tr><td class="fw-bold">Name:</td><td>${vm.name || '-'}</td></tr>
                                    <tr><td class="fw-bold">Type:</td><td>${vm.instance_type}</td></tr>
                                    <tr><td class="fw-bold">State:</td><td><span class="badge bg-${getVMStateColor(vm.state)}">${vm.state}</span></td></tr>
                                    <tr><td class="fw-bold">vCPU:</td><td>${vm.vcpu_count || '-'}</td></tr>
                                    <tr><td class="fw-bold">Memory:</td><td>${vm.memory_gb ? vm.memory_gb + ' GB' : '-'}</td></tr>
                                    <tr><td class="fw-bold">OS Type:</td><td>${vm.os_type || '-'}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header py-1"><small class="fw-bold">Network & Security</small></div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                    <tr><td class="fw-bold" style="width: 40%;">Private IP:</td><td>${vm.private_ip_address || '-'}</td></tr>
                                    <tr><td class="fw-bold">Public IP:</td><td>${vm.public_ip_address || '-'}</td></tr>
                                    <tr><td class="fw-bold">FQDN:</td><td class="small">${vm.fqdn || '-'}</td></tr>
                                    <tr><td class="fw-bold">Virtual Network:</td><td class="small">${vm.virtual_network || '-'}</td></tr>
                                    <tr><td class="fw-bold">Subnet:</td><td class="small">${vm.subnet || '-'}</td></tr>
                                    <tr><td class="fw-bold">NSG:</td><td class="small">${vm.network_security_group || '-'}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header py-1"><small class="fw-bold">Account & Storage</small></div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                    <tr><td class="fw-bold" style="width: 40%;">Business Unit:</td><td>${vm.business_unit || '-'}</td></tr>
                                    <tr><td class="fw-bold">Subscription:</td><td>${vm.subscription_name}</td></tr>
                                    <tr><td class="fw-bold">Location:</td><td>${vm.location}</td></tr>
                                    <tr><td class="fw-bold">Resource Group:</td><td class="small">${vm.resource_group || '-'}</td></tr>
                                    <tr><td class="fw-bold">OS Disk Size:</td><td>${vm.os_disk_size_gb ? vm.os_disk_size_gb + ' GB' : '-'}</td></tr>
                                    <tr><td class="fw-bold">Data Disks:</td><td>${vm.data_disk_count || 0}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-2 mt-1">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header py-1"><small class="fw-bold">Tags</small></div>
                            <div class="card-body p-2">
                                <div class="row g-1" style="font-size: 0.75rem;">
                                    <div class="col-6"><span class="fw-bold">Environment:</span> ${vm.env_tag || '-'}</div>
                                    <div class="col-6"><span class="fw-bold">UAI:</span> ${vm.uai_tag || '-'}</div>
                                    <div class="col-6"><span class="fw-bold">CTO Managed:</span> ${vm.cto_cloud_ops_managed || '-'}</div>
                                    <div class="col-6"><span class="fw-bold">Patch Group:</span> ${vm.patch_group || '-'}</div>
                                    <div class="col-6"><span class="fw-bold">Backup:</span> ${vm.backup_tag || '-'}</div>
                                    <div class="col-6"><span class="fw-bold">Schedule:</span> ${vm.auto_shutdown_schedule || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        ${vm.agent_status ? `
                        <div class="card">
                            <div class="card-header py-1"><small class="fw-bold">VM Agent Status</small></div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                    <tr><td class="fw-bold" style="width: 40%;">Status:</td><td><span class="badge bg-${getAgentColor(vm.agent_status.agent_status)}">${vm.agent_status.agent_status}</span></td></tr>
                                    <tr><td class="fw-bold">Agent Version:</td><td>${vm.agent_status.agent_version || '-'}</td></tr>
                                    <tr><td class="fw-bold">Extensions:</td><td>${vm.agent_status.extensions_count || 0}</td></tr>
                                    <tr><td class="fw-bold">Boot Diagnostics:</td><td>${vm.agent_status.boot_diagnostics_enabled ? 'Enabled' : 'Disabled'}</td></tr>
                                    <tr><td class="fw-bold">Last Updated:</td><td class="small">${formatDate(vm.agent_status.last_updated)}</td></tr>
                                </table>
                            </div>
                        </div>
                        ` : `
                        <div class="card">
                            <div class="card-header py-1"><small class="fw-bold">VM Agent Status</small></div>
                            <div class="card-body p-2 text-center text-muted">
                                <small>No VM Agent data available</small>
                            </div>
                        </div>
                        `}
                    </div>
                </div>

                ${vm.vm_tags && vm.vm_tags.length > 0 ? `
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header py-1"><small class="fw-bold">All Tags (${vm.vm_tags.length})</small></div>
                            <div class="card-body p-2">
                                <div class="d-flex flex-wrap gap-1">
                                    ${vm.vm_tags.map(tag => `
                                        <span class="badge bg-light text-dark border" style="font-size: 0.7rem;">
                                            <strong>${tag.key}</strong>=<span class="text-muted">${tag.value}</span>
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}
            `;

            const modal = new bootstrap.Modal(document.getElementById('vmDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error loading VM details:', error);
            showNotification('Error loading VM details', 'error');
        });
}

// Update statistics
function updateStats() {
    fetch('/api/azure-vms/?page_size=1000')
        .then(response => response.json())
        .then(data => {
            const vms = data.results;
            const totalVMs = vms.length;
            const runningVMs = vms.filter(vm => vm.state === 'VM running').length;
            const agentReady = vms.filter(vm => vm.agent_status === 'Ready').length;

            // Get unique subscriptions
            const uniqueSubscriptions = new Set(vms.map(vm => vm.subscription_id));

            document.getElementById('total-vms').textContent = totalVMs;
            document.getElementById('running-vms').textContent = runningVMs;
            document.getElementById('agent-ready').textContent = agentReady;
            document.getElementById('total-subscriptions').textContent = uniqueSubscriptions.size;
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

// Load filter options
function loadFilters() {
    // Load business units and subscriptions
    fetch('/api/azure-subscriptions/')
        .then(response => response.json())
        .then(data => {
            const businessUnits = [...new Set(data.results.map(sub => sub.business_unit))];
            const buSelect = document.getElementById('business-unit-filter');
            businessUnits.forEach(bu => {
                const option = document.createElement('option');
                option.value = bu;
                option.textContent = bu;
                buSelect.appendChild(option);
            });

            // Store all subscriptions for filtering
            window.allSubscriptions = data.results;
            updateSubscriptionFilter();
        });

    // Load locations
    fetch('/api/azure-vms/?page_size=1000')
        .then(response => response.json())
        .then(data => {
            const locations = [...new Set(data.results.map(vm => vm.location))];
            const locationSelect = document.getElementById('location-filter');
            locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                locationSelect.appendChild(option);
            });
        });
}

// Update subscription filter based on selected business unit
function updateSubscriptionFilter() {
    const buFilter = document.getElementById('business-unit-filter');
    const subFilter = document.getElementById('subscription-filter');
    const selectedBU = buFilter.value;

    // Clear existing options except the first one
    subFilter.innerHTML = '<option value="">All Subscriptions</option>';

    if (window.allSubscriptions) {
        const filteredSubs = selectedBU
            ? window.allSubscriptions.filter(sub => sub.business_unit === selectedBU)
            : window.allSubscriptions;

        filteredSubs.forEach(sub => {
            const option = document.createElement('option');
            option.value = sub.subscription_id;
            option.textContent = sub.subscription_name;
            subFilter.appendChild(option);
        });
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadVMs();
    loadFilters();

    // Setup search
    document.getElementById('search-input').addEventListener('input', debounce(function() {
        currentFilters.search = this.value;
        loadVMs(1);
    }, 500));

    // Setup filter changes
    document.getElementById('business-unit-filter').addEventListener('change', function() {
        currentFilters.business_unit = this.value;
        updateSubscriptionFilter();
        loadVMs(1);
    });

    ['subscription-filter', 'state-filter', 'location-filter'].forEach(id => {
        document.getElementById(id).addEventListener('change', function() {
            const filterName = id.replace('-filter', '').replace('-', '_');
            if (id === 'subscription-filter') {
                currentFilters.subscription_id = this.value;
            } else {
                currentFilters[filterName] = this.value;
            }
            loadVMs(1);
        });
    });
});

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function getVMStateColor(state) {
    switch(state) {
        case 'VM running': return 'success';
        case 'VM stopped': return 'danger';
        case 'VM starting': return 'warning';
        case 'VM stopping': return 'warning';
        case 'VM deallocated': return 'secondary';
        default: return 'secondary';
    }
}

function getAgentColor(status) {
    switch(status) {
        case 'Ready': return 'success';
        case 'NotReady': return 'warning';
        case 'Unknown': return 'secondary';
        default: return 'secondary';
    }
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString();
}

function showNotification(message, type = 'info') {
    // Implementation for notifications
    console.log(`${type}: ${message}`);
}

function refreshInventory() {
    showNotification('Refreshing Azure VM inventory...', 'info');
    fetch('/api/refresh-azure-inventory/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showNotification('Azure VM inventory refreshed successfully', 'success');
            loadVMs();
        } else {
            showNotification('Failed to refresh inventory: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Error refreshing inventory', 'error');
    });
}

function exportData() {
    showNotification('Exporting Azure VM data...', 'info');
    window.open('/api/azure-vms/export/', '_blank');
}

function clearFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('business-unit-filter').value = '';
    document.getElementById('subscription-filter').value = '';
    document.getElementById('state-filter').value = '';
    document.getElementById('location-filter').value = '';
    currentFilters = {};
    updateSubscriptionFilter(); // Reset subscription filter
    loadVMs(1);
}
</script>
{% endblock %}
