<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Cloud Operations Central{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --sidebar-width: 260px;
            --header-height: 50px;
            --footer-height: 40px;
            --compact-padding: 8px;
            --compact-margin: 6px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f6fa;
            margin: 0;
            padding: 0;
            font-size: 0.875rem;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Modern Compact Design */
        .compact {
            padding: var(--compact-padding) !important;
            margin: var(--compact-margin) !important;
        }

        .compact-card {
            margin-bottom: 12px;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.2s ease;
            background: white;
        }

        .compact-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .compact-card .card-body {
            padding: 16px;
        }

        .compact-card .card-header {
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #e9ecef;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            font-size: 0.875rem;
            color: #495057;
        }

        .compact-form {
            margin-bottom: 12px;
        }

        .compact-form .form-control,
        .compact-form .form-select {
            padding: 8px 12px;
            font-size: 0.875rem;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .compact-form .form-control:focus,
        .compact-form .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
        }

        .compact-form .btn {
            padding: 8px 16px;
            font-size: 0.875rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        /* Header Styles */
        .main-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1030;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 0 15px;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .app-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: var(--header-height);
            left: 0;
            width: var(--sidebar-width);
            height: calc(100vh - var(--header-height) - var(--footer-height));
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            overflow-y: auto;
            transition: transform 0.3s ease;
            z-index: 1020;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-nav {
            padding: 10px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 0.875rem;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background-color: var(--accent-color);
            color: white;
            border-left: 4px solid white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .nav-link .nav-text {
            flex: 1;
        }

        .nav-link .toggle-icon {
            transition: transform 0.3s;
        }

        .nav-link[aria-expanded="true"] .toggle-icon {
            transform: rotate(90deg);
        }

        .collapse .nav-link {
            padding-left: 30px;
            font-size: 0.8rem;
        }

        .collapse .collapse .nav-link {
            padding-left: 45px;
            font-size: 0.75rem;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            margin-bottom: var(--footer-height);
            padding: 15px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - var(--header-height) - var(--footer-height));
            overflow-x: auto;
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }

        /* Footer Styles */
        .main-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--footer-height);
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1010;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 6px;
            padding: 12px 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 3px solid var(--accent-color);
        }

        .page-title {
            margin: 0;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.25rem;
        }

        /* Compact Tables */
        .table-compact {
            font-size: 0.8rem;
        }

        .table-compact th,
        .table-compact td {
            padding: 0.4rem 0.6rem;
            vertical-align: middle;
            border: 1px solid #e9ecef;
        }

        .table-compact th {
            background-color: #f8f9fa;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table-compact .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .table-compact .badge {
            font-size: 0.7rem;
            padding: 0.25em 0.5em;
        }

        /* Compact Forms */
        .filter-section {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .filter-section .form-control,
        .filter-section .form-select {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
            height: calc(1.5em + 0.75rem + 2px);
        }

        .filter-section .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Autocomplete Styling */
        .autocomplete-container {
            position: relative;
        }

        .autocomplete-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .autocomplete-suggestion {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.875rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .autocomplete-suggestion:hover,
        .autocomplete-suggestion.active {
            background-color: #f8f9fa;
        }

        .autocomplete-suggestion:last-child {
            border-bottom: none;
        }

        /* Modern Cards */
        .stats-card {
            border: none;
            border-radius: 12px;
            background: white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 12px;
            overflow: hidden;
            position: relative;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), #5dade2);
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .stats-card .card-body {
            padding: 20px 18px 18px 18px;
            text-align: center;
        }

        .stats-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 4px;
            color: #2c3e50;
            line-height: 1;
        }

        .stats-card h6 {
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stats-card.bg-primary h3 { color: white; }
        .stats-card.bg-success h3 { color: white; }
        .stats-card.bg-warning h3 { color: white; }
        .stats-card.bg-info h3 { color: white; }
        .stats-card.bg-danger h3 { color: white; }

        .stats-card.bg-primary h6 { color: rgba(255,255,255,0.8); }
        .stats-card.bg-success h6 { color: rgba(255,255,255,0.8); }
        .stats-card.bg-warning h6 { color: rgba(255,255,255,0.8); }
        .stats-card.bg-info h6 { color: rgba(255,255,255,0.8); }
        .stats-card.bg-danger h6 { color: rgba(255,255,255,0.8); }

        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 12px;
            background: white;
            transition: all 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #e9ecef;
            border-radius: 8px 8px 0 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
        }

        .card-body {
            padding: 16px;
        }

        .card-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        /* Utilities */
        .loading {
            display: none;
        }

        .alert-custom {
            margin-top: 10px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 10px;
            }

            .header-content {
                padding: 0 10px;
            }

            .app-title {
                font-size: 1rem;
            }

            .page-header {
                padding: 8px 10px;
                margin-bottom: 10px;
            }

            .page-title {
                font-size: 1.1rem;
            }

            .filter-section {
                padding: 8px;
                margin-bottom: 10px;
            }

            .table-compact {
                font-size: 0.75rem;
            }

            .table-compact th,
            .table-compact td {
                padding: 0.3rem 0.4rem;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 8px;
            }

            .stats-card .card-body {
                padding: 10px;
            }

            .stats-card h3 {
                font-size: 1.25rem;
            }

            .card-body {
                padding: 10px;
            }

            .filter-section {
                padding: 6px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="app-title">Cloud Operations Central</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle fa-lg"></i>
                    <span>Admin User</span>
                </div>
                <div class="dropdown">
                    <button class="btn btn-link text-white dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/"><i class="fas fa-tools me-2"></i>Admin Panel</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-nav">
            <!-- Dashboard -->
            <div class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'inventory:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </div>

            <!-- Inventory -->
            <div class="nav-item">
                <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#inventoryMenu" aria-expanded="false">
                    <i class="fas fa-database"></i>
                    <span class="nav-text">Inventory</span>
                    <i class="fas fa-chevron-right toggle-icon"></i>
                </button>
                <div class="collapse" id="inventoryMenu">
                    <!-- AWS -->
                    <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#awsMenu" aria-expanded="false">
                        <i class="fab fa-aws"></i>
                        <span class="nav-text">AWS</span>
                        <i class="fas fa-chevron-right toggle-icon"></i>
                    </button>
                    <div class="collapse" id="awsMenu">
                        <a class="nav-link {% if request.resolver_match.url_name == 'ec2_instances' %}active{% endif %}" href="{% url 'inventory:ec2_instances' %}">
                            <i class="fas fa-server"></i>
                            <span class="nav-text">EC2 Instances</span>
                        </a>
                        <a class="nav-link {% if request.resolver_match.url_name == 'eks_clusters' %}active{% endif %}" href="{% url 'inventory:eks_clusters' %}">
                            <i class="fas fa-cubes"></i>
                            <span class="nav-text">EKS Clusters</span>
                        </a>
                    </div>

                    <!-- Azure -->
                    <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#azureMenu" aria-expanded="false">
                        <i class="fab fa-microsoft"></i>
                        <span class="nav-text">Azure</span>
                        <i class="fas fa-chevron-right toggle-icon"></i>
                    </button>
                    <div class="collapse" id="azureMenu">
                        <a class="nav-link" href="{% url 'inventory:azure_vms' %}">
                            <i class="fas fa-desktop"></i>
                            <span class="nav-text">Virtual Machines</span>
                        </a>
                        <a class="nav-link" href="#" onclick="showPlaceholder('AKS Clusters')">
                            <i class="fas fa-cubes"></i>
                            <span class="nav-text">AKS Clusters</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Automation Deploy -->
            <div class="nav-item">
                <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#automationMenu" aria-expanded="false">
                    <i class="fas fa-robot"></i>
                    <span class="nav-text">Automation Deploy</span>
                    <i class="fas fa-chevron-right toggle-icon"></i>
                </button>
                <div class="collapse" id="automationMenu">
                    <a class="nav-link" href="#" onclick="showPlaceholder('Run Deployment')">
                        <i class="fas fa-play"></i>
                        <span class="nav-text">Run Deployment</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Automation Jobs')">
                        <i class="fas fa-tasks"></i>
                        <span class="nav-text">Automation Jobs</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Automation Reports')">
                        <i class="fas fa-chart-line"></i>
                        <span class="nav-text">Automation Reports</span>
                    </a>
                </div>
            </div>

            <!-- Decommission -->
            <div class="nav-item">
                <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#decomMenu" aria-expanded="false">
                    <i class="fas fa-trash-alt"></i>
                    <span class="nav-text">Decommission</span>
                    <i class="fas fa-chevron-right toggle-icon"></i>
                </button>
                <div class="collapse" id="decomMenu">
                    <a class="nav-link" href="#" onclick="showPlaceholder('AWS Resource Decom')">
                        <i class="fab fa-aws"></i>
                        <span class="nav-text">AWS Resource Decom</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Azure Resource Decom')">
                        <i class="fab fa-microsoft"></i>
                        <span class="nav-text">Azure Resource Decom</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Decom Jobs')">
                        <i class="fas fa-tasks"></i>
                        <span class="nav-text">Decom Jobs</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Decom Reports')">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-text">Decom Reports</span>
                    </a>
                </div>
            </div>

            <!-- App Configuration -->
            <div class="nav-item">
                <button class="nav-link" type="button" data-bs-toggle="collapse" data-bs-target="#configMenu" aria-expanded="false">
                    <i class="fas fa-cogs"></i>
                    <span class="nav-text">App Configuration</span>
                    <i class="fas fa-chevron-right toggle-icon"></i>
                </button>
                <div class="collapse" id="configMenu">
                    <a class="nav-link" href="#" onclick="showPlaceholder('General Settings')">
                        <i class="fas fa-sliders-h"></i>
                        <span class="nav-text">General Settings</span>
                    </a>
                    <a class="nav-link {% if request.resolver_match.url_name == 'accounts' %}active{% endif %}" href="{% url 'inventory:accounts' %}">
                        <i class="fab fa-aws"></i>
                        <span class="nav-text">AWS Accounts Manager</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Azure Subscriptions Manager')">
                        <i class="fab fa-microsoft"></i>
                        <span class="nav-text">Azure Subscriptions Manager</span>
                    </a>
                    <a class="nav-link" href="#" onclick="showPlaceholder('Jobs Manager')">
                        <i class="fas fa-clock"></i>
                        <span class="nav-text">Jobs</span>
                    </a>
                    {% if user.is_authenticated and user.is_admin %}
                    <a class="nav-link {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}" href="{% url 'accounts:user_list' %}">
                        <i class="fas fa-users-cog"></i>
                        <span class="nav-text">User Management</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Page Header -->
        <!-- <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">{% block page_title %}Dashboard{% endblock %}</h1>
                <div class="btn-toolbar">
                    {% block page_actions %}
                    <button type="button" class="btn btn-success" onclick="refreshAllInventory()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh All
                    </button>
                    {% endblock %}
                </div>
            </div>
        </div> -->

        <!-- Alert container -->
        <div id="alert-container"></div>

        <!-- Loading indicator -->
        <div class="loading text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading data...</p>
        </div>

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="text-center">
            <small>&copy; 2024 Cloud Operations Central. All rights reserved.</small>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // CSRF token for AJAX requests
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('sidebar-collapsed');

                // Store sidebar state in localStorage
                const isCollapsed = sidebar.classList.contains('collapsed');
                localStorage.setItem('sidebarCollapsed', isCollapsed);
            });

            // Restore sidebar state from localStorage
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
            }

            // Handle responsive sidebar
            function handleResize() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('sidebar-collapsed');
                } else {
                    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                    if (!sidebarCollapsed) {
                        sidebar.classList.remove('collapsed');
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize(); // Initial check
        });

        // Show placeholder message for unimplemented features
        function showPlaceholder(featureName) {
            showAlert(`${featureName} feature is coming soon!`, 'info');
        }

        // Show alert function
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-custom" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alert-container').html(alertHtml);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Show/hide loading
        function showLoading() {
            $('.loading').show();
        }

        function hideLoading() {
            $('.loading').hide();
        }

        // Refresh all inventory
        function refreshAllInventory() {
            if (confirm('This will refresh inventory for all accounts. This may take several minutes. Continue?')) {
                showLoading();

                $.ajax({
                    url: '/api/refresh-all/',
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrftoken
                    },
                    success: function(response) {
                        hideLoading();
                        showAlert(response.message, 'success');
                        // Reload page data if we're on a data page
                        if (typeof loadData === 'function') {
                            loadData();
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        const response = xhr.responseJSON || {};
                        showAlert(response.message || 'Failed to refresh inventory', 'danger');
                    }
                });
            }
        }

        // Format date function
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Autocomplete functionality
        function initializeAutocomplete(inputElement, dataSource, onSelect) {
            const container = document.createElement('div');
            container.className = 'autocomplete-container';
            inputElement.parentNode.insertBefore(container, inputElement);
            container.appendChild(inputElement);

            const suggestionsDiv = document.createElement('div');
            suggestionsDiv.className = 'autocomplete-suggestions';
            suggestionsDiv.style.display = 'none';
            container.appendChild(suggestionsDiv);

            let currentSuggestions = [];
            let selectedIndex = -1;

            inputElement.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                if (value.length < 1) {
                    hideSuggestions();
                    return;
                }

                if (typeof dataSource === 'function') {
                    dataSource(value, showSuggestions);
                } else {
                    const filtered = dataSource.filter(item =>
                        item.toLowerCase().includes(value)
                    );
                    showSuggestions(filtered);
                }
            });

            inputElement.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1);
                    updateSelection();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection();
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (selectedIndex >= 0) {
                        selectSuggestion(currentSuggestions[selectedIndex]);
                    }
                } else if (e.key === 'Escape') {
                    hideSuggestions();
                }
            });

            inputElement.addEventListener('blur', function() {
                setTimeout(hideSuggestions, 150);
            });

            function showSuggestions(suggestions) {
                currentSuggestions = suggestions.slice(0, 10); // Limit to 10 suggestions
                selectedIndex = -1;

                if (currentSuggestions.length === 0) {
                    hideSuggestions();
                    return;
                }

                suggestionsDiv.innerHTML = '';
                currentSuggestions.forEach((suggestion, index) => {
                    const div = document.createElement('div');
                    div.className = 'autocomplete-suggestion';
                    div.textContent = suggestion;
                    div.addEventListener('click', () => selectSuggestion(suggestion));
                    suggestionsDiv.appendChild(div);
                });

                suggestionsDiv.style.display = 'block';
            }

            function hideSuggestions() {
                suggestionsDiv.style.display = 'none';
                selectedIndex = -1;
            }

            function updateSelection() {
                const suggestions = suggestionsDiv.querySelectorAll('.autocomplete-suggestion');
                suggestions.forEach((suggestion, index) => {
                    suggestion.classList.toggle('active', index === selectedIndex);
                });
            }

            function selectSuggestion(suggestion) {
                inputElement.value = suggestion;
                hideSuggestions();
                if (onSelect) {
                    onSelect(suggestion);
                }
                inputElement.dispatchEvent(new Event('change'));
            }
        }

        // Debounce function for search inputs
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
