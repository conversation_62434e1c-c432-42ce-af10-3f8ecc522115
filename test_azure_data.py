#!/usr/bin/env python
"""
Test script to verify Azure data loading
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AzureVM, AzureSubscription, VMTag

try:
    # Check counts
    subscription_count = AzureSubscription.objects.count()
    vm_count = AzureVM.objects.count()
    tag_count = VMTag.objects.count()
    
    print(f"✅ Azure Data Summary:")
    print(f"   - Subscriptions: {subscription_count}")
    print(f"   - VMs: {vm_count}")
    print(f"   - Tags: {tag_count}")
    
    if subscription_count > 0:
        print(f"\n📋 Sample Subscriptions:")
        for sub in AzureSubscription.objects.all()[:3]:
            print(f"   - {sub.subscription_name} ({sub.business_unit})")
    
    if vm_count > 0:
        print(f"\n🖥️  Sample VMs:")
        for vm in AzureVM.objects.all()[:5]:
            print(f"   - {vm.name} ({vm.instance_type}) - {vm.state} - {vm.env_tag}")
    
    # Check specific fields
    if vm_count > 0:
        sample_vm = AzureVM.objects.first()
        print(f"\n🔍 Sample VM Details:")
        print(f"   - Name: {sample_vm.name}")
        print(f"   - Instance Type: {sample_vm.instance_type}")
        print(f"   - OS Version: {sample_vm.os_version}")
        print(f"   - UAI: {sample_vm.uai_tag}")
        print(f"   - Environment: {sample_vm.env_tag}")
        print(f"   - CTO Managed: {sample_vm.cto_cloud_ops_managed}")
        print(f"   - Private IP: {sample_vm.private_ip_address}")
    
    print(f"\n🎉 Azure data verification completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {str(e)}")
    import traceback
    traceback.print_exc()
